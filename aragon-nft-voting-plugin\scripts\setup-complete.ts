import {ethers} from 'hardhat';
import deployPlugin from './deploy-plugin';
import installPlugin from './install-plugin';
import testPlugin from './test-plugin';

interface SetupConfig {
  deployContracts: boolean;
  installToDAO: boolean;
  runTests: boolean;
  daoAddress?: string;
  nftContractAddress?: string;
  createTestNFT?: boolean;
  votingSettings?: {
    votingMode: number;
    supportThreshold: number;
    minParticipation: number;
    minDuration: number;
    minProposerVotingPower: number;
  };
}

async function main() {
  console.log('🚀 NFT Voting Plugin Complete Setup');
  console.log('====================================\n');

  const [deployer] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log('Setup Information:');
  console.log(`Network: ${network.name} (${network.chainId})`);
  console.log(`Deployer: ${deployer.address}`);
  console.log(`Balance: ${ethers.utils.formatEther(await deployer.getBalance())} ETH\n`);

  // Configuration from environment variables
  const config: SetupConfig = {
    deployContracts: process.env.DEPLOY_CONTRACTS !== 'false',
    installToDAO: process.env.INSTALL_TO_DAO === 'true',
    runTests: process.env.RUN_TESTS !== 'false',
    daoAddress: process.env.DAO_ADDRESS,
    nftContractAddress: process.env.NFT_CONTRACT_ADDRESS,
    createTestNFT: process.env.CREATE_TEST_NFT === 'true',
    votingSettings: {
      votingMode: parseInt(process.env.VOTING_MODE || '0'),
      supportThreshold: parseInt(process.env.SUPPORT_THRESHOLD || '500000'),
      minParticipation: parseInt(process.env.MIN_PARTICIPATION || '250000'),
      minDuration: parseInt(process.env.MIN_DURATION || '86400'),
      minProposerVotingPower: parseInt(process.env.MIN_PROPOSER_VOTING_POWER || '1'),
    },
  };

  console.log('Configuration:');
  console.log(`Deploy Contracts: ${config.deployContracts}`);
  console.log(`Install to DAO: ${config.installToDAO}`);
  console.log(`Run Tests: ${config.runTests}`);
  console.log(`Create Test NFT: ${config.createTestNFT}`);
  console.log(`DAO Address: ${config.daoAddress || 'Not provided'}`);
  console.log(`NFT Contract: ${config.nftContractAddress || 'Not provided'}\n`);

  let deploymentResult: any;
  let nftAddress = config.nftContractAddress;
  let daoAddress = config.daoAddress;

  // Step 1: Deploy contracts
  if (config.deployContracts) {
    console.log('📦 Step 1: Deploying Plugin Contracts');
    console.log('-'.repeat(40));
    
    try {
      deploymentResult = await deployPlugin();
      console.log('✅ Plugin contracts deployed successfully\n');
    } catch (error) {
      console.error('❌ Plugin deployment failed:', error);
      process.exit(1);
    }
  } else {
    console.log('⏭️  Step 1: Skipping contract deployment\n');
  }

  // Step 2: Create test NFT if needed
  if (config.createTestNFT && !nftAddress) {
    console.log('🎨 Step 2: Creating Test NFT Contract');
    console.log('-'.repeat(40));
    
    try {
      const MockNFTFactory = await ethers.getContractFactory('MockNFT');
      const mockNFT = await MockNFTFactory.deploy(
        'DAO Voting NFT',
        'DVNFT',
        'https://dao.example.com/nft/metadata/'
      );
      await mockNFT.deployed();
      
      nftAddress = mockNFT.address;
      console.log(`✅ Test NFT deployed at: ${nftAddress}`);
      
      // Mint some test NFTs
      const [, alice, bob, charlie] = await ethers.getSigners();
      await mockNFT.mint(alice.address);
      await mockNFT.mint(bob.address);
      await mockNFT.mint(charlie.address);
      await mockNFT.mintBatch(alice.address, 2);
      
      console.log('✅ Test NFTs minted to test accounts\n');
    } catch (error) {
      console.error('❌ Test NFT creation failed:', error);
      process.exit(1);
    }
  } else if (!config.createTestNFT) {
    console.log('⏭️  Step 2: Skipping test NFT creation\n');
  }

  // Step 3: Create test DAO if needed
  if (!daoAddress && (config.installToDAO || config.runTests)) {
    console.log('🏛️  Step 3: Creating Test DAO');
    console.log('-'.repeat(40));
    
    try {
      const MockDAOFactory = await ethers.getContractFactory('MockDAO');
      const mockDAO = await MockDAOFactory.deploy();
      await mockDAO.deployed();
      
      daoAddress = mockDAO.address;
      console.log(`✅ Test DAO deployed at: ${daoAddress}\n`);
    } catch (error) {
      console.error('❌ Test DAO creation failed:', error);
      process.exit(1);
    }
  } else if (!config.installToDAO && !config.runTests) {
    console.log('⏭️  Step 3: Skipping test DAO creation\n');
  }

  // Step 4: Install plugin to DAO
  if (config.installToDAO && deploymentResult && nftAddress && daoAddress) {
    console.log('🔧 Step 4: Installing Plugin to DAO');
    console.log('-'.repeat(40));
    
    // Set environment variables for installation script
    process.env.DAO_ADDRESS = daoAddress;
    process.env.NFT_CONTRACT_ADDRESS = nftAddress;
    process.env.PLUGIN_SETUP_ADDRESS = deploymentResult.pluginSetup;
    
    // Set voting settings
    if (config.votingSettings) {
      process.env.VOTING_MODE = config.votingSettings.votingMode.toString();
      process.env.SUPPORT_THRESHOLD = config.votingSettings.supportThreshold.toString();
      process.env.MIN_PARTICIPATION = config.votingSettings.minParticipation.toString();
      process.env.MIN_DURATION = config.votingSettings.minDuration.toString();
      process.env.MIN_PROPOSER_VOTING_POWER = config.votingSettings.minProposerVotingPower.toString();
    }
    
    try {
      const installationResult = await installPlugin();
      console.log('✅ Plugin installation prepared successfully\n');
    } catch (error) {
      console.error('❌ Plugin installation failed:', error);
      process.exit(1);
    }
  } else if (!config.installToDAO) {
    console.log('⏭️  Step 4: Skipping plugin installation\n');
  } else {
    console.log('⚠️  Step 4: Cannot install plugin - missing required addresses\n');
  }

  // Step 5: Run tests
  if (config.runTests) {
    console.log('🧪 Step 5: Running Plugin Tests');
    console.log('-'.repeat(40));
    
    try {
      await testPlugin();
      console.log('✅ Plugin tests completed successfully\n');
    } catch (error) {
      console.error('❌ Plugin tests failed:', error);
      process.exit(1);
    }
  } else {
    console.log('⏭️  Step 5: Skipping plugin tests\n');
  }

  // Final summary
  console.log('🎉 Setup Complete!');
  console.log('==================');
  
  if (deploymentResult) {
    console.log(`Plugin Implementation: ${deploymentResult.pluginImplementation}`);
    console.log(`Plugin Setup:          ${deploymentResult.pluginSetup}`);
  }
  
  if (nftAddress) {
    console.log(`NFT Contract:          ${nftAddress}`);
  }
  
  if (daoAddress) {
    console.log(`DAO Contract:          ${daoAddress}`);
  }
  
  console.log(`Network:               ${network.name} (${network.chainId})`);
  console.log(`Deployer:              ${deployer.address}`);
  
  console.log('\n📋 Next Steps:');
  console.log('1. Verify contracts on block explorer (if on public network)');
  console.log('2. Grant required permissions through DAO governance');
  console.log('3. Test plugin functionality with real proposals');
  console.log('4. Set up monitoring and alerts');
  console.log('5. Create user documentation');
  
  console.log('\n📚 Documentation:');
  console.log('- Plugin Repository: https://docs.aragon.org/osx/');
  console.log('- NFT Voting Guide: ./docs/user-guide.md');
  console.log('- API Reference: ./docs/api.md');
  
  return {
    deployment: deploymentResult,
    nftContract: nftAddress,
    daoContract: daoAddress,
    network: network.name,
    chainId: network.chainId,
  };
}

// Execute setup if this script is run directly
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n✅ Complete setup finished successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Setup failed:', error);
      process.exit(1);
    });
}

export default main;
