# Contributing to NFT Minting Platform

Thank you for your interest in contributing to the NFT Minting Platform! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Git
- MongoDB Atlas account
- Basic knowledge of React, Node.js, and Ethereum

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/yourusername/nft-minting-platform.git
   cd nft-minting-platform
   ```

2. **Install Dependencies**
   ```bash
   npm run install-all
   ```

3. **Environment Setup**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your configuration
   ```

4. **Start Development**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
nft-minting-platform/
├── frontend/          # Next.js React application
├── backend/           # Node.js Express API
├── contracts/         # Smart contracts (Hardhat)
├── docs/             # Documentation
└── scripts/          # Deployment and utility scripts
```

## 🔧 Development Guidelines

### Code Style
- Use TypeScript for frontend
- Use ESLint and Prettier
- Follow conventional commit messages
- Write meaningful variable and function names

### Frontend (Next.js)
- Use functional components with hooks
- Implement proper error boundaries
- Follow responsive design principles
- Use Tailwind CSS for styling

### Backend (Node.js)
- Use async/await for asynchronous operations
- Implement proper error handling
- Use environment variables for configuration
- Follow RESTful API conventions

### Smart Contracts
- Use Hardhat for development
- Write comprehensive tests
- Follow OpenZeppelin standards
- Document all functions

## 🧪 Testing

### Running Tests
```bash
# Backend tests
cd backend && npm test

# Frontend tests
cd frontend && npm test

# Smart contract tests
cd backend && npm run test:contracts
```

### Test Coverage
- Aim for >80% test coverage
- Write unit tests for all functions
- Include integration tests for APIs
- Test smart contract edge cases

## 📝 Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write clean, documented code
   - Add tests for new functionality
   - Update documentation if needed

3. **Test Your Changes**
   ```bash
   npm run test
   npm run lint
   ```

4. **Commit Changes**
   ```bash
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

### PR Requirements
- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
- [ ] Reviewed by at least one maintainer

## 🐛 Bug Reports

When reporting bugs, please include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node version, etc.)
- Screenshots if applicable

## 💡 Feature Requests

For new features:
- Check existing issues first
- Provide clear use case
- Explain expected behavior
- Consider implementation complexity

## 🔒 Security

- Never commit private keys or sensitive data
- Use environment variables for secrets
- Report security issues privately
- Follow blockchain security best practices

## 📚 Documentation

- Update README for new features
- Document API changes
- Include code comments
- Update environment variable examples

## 🎯 Roadmap

Current priorities:
1. Smart contract integration
2. Enhanced UI/UX
3. Multi-chain support
4. Advanced NFT features
5. Performance optimization

## 📞 Getting Help

- Create an issue for bugs
- Use discussions for questions
- Join our Discord community
- Check existing documentation

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to the NFT Minting Platform! 🚀
