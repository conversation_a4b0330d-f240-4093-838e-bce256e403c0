import {ethers} from 'hardhat';

async function main() {
  console.log('🔍 Verifying Your Deployed WE3HomeNFT Contract');
  console.log('='.repeat(50));

  // Your deployed contract details
  const contractAddress = '******************************************';
  const network = 'polygon-amoy'; // Polygon Amoy testnet
  
  console.log(`Contract Address: ${contractAddress}`);
  console.log(`Network: ${network}`);
  console.log(`RPC: ${process.env.POLYGON_TESTNET_RPC}`);

  // Connect to your deployed contract
  const provider = new ethers.providers.JsonRpcProvider(process.env.POLYGON_TESTNET_RPC);
  const wallet = new ethers.Wallet(process.env.PRIVATE_KEY!, provider);
  
  console.log(`Connected wallet: ${wallet.address}`);
  console.log(`Wallet balance: ${ethers.utils.formatEther(await wallet.getBalance())} MATIC\n`);

  // Get contract instance
  const we3NFT = await ethers.getContractAt('WE3HomeNFT', contractAddress, wallet);

  try {
    console.log('📋 Contract Information:');
    console.log('-'.repeat(30));
    
    // Basic contract info
    const name = await we3NFT.name();
    const symbol = await we3NFT.symbol();
    const totalSupply = await we3NFT.totalSupply();
    const maxSupply = await we3NFT.maxSupply();
    const maxPerWallet = await we3NFT.maxPerWallet();
    const owner = await we3NFT.owner();
    const backendAddress = await we3NFT.backendAddress();
    const baseURI = await we3NFT.baseTokenURI();

    console.log(`Name: ${name}`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Total Supply: ${totalSupply}`);
    console.log(`Max Supply: ${maxSupply}`);
    console.log(`Max Per Wallet: ${maxPerWallet}`);
    console.log(`Owner: ${owner}`);
    console.log(`Backend Address: ${backendAddress}`);
    console.log(`Base URI: ${baseURI}`);

    console.log('\n✅ Contract is accessible and working!');

    // Check voting plugin compatibility
    console.log('\n🗳️  Voting Plugin Compatibility Check:');
    console.log('-'.repeat(40));

    // Check ERC721 interface support
    const supportsERC721 = await we3NFT.supportsInterface('0x80ac58cd');
    const supportsMetadata = await we3NFT.supportsInterface('0x5b5e139f');
    
    console.log(`ERC721 Interface: ${supportsERC721 ? '✅' : '❌'}`);
    console.log(`Metadata Interface: ${supportsMetadata ? '✅' : '❌'}`);

    // Check required functions exist
    const requiredFunctions = ['balanceOf', 'ownerOf', 'totalSupply', 'supportsInterface'];
    console.log('\nRequired Functions:');
    for (const func of requiredFunctions) {
      const exists = typeof we3NFT[func] === 'function';
      console.log(`${func}: ${exists ? '✅' : '❌'}`);
    }

    // Test voting power calculation
    console.log('\n📊 Voting Power Analysis:');
    console.log('-'.repeat(30));
    console.log(`Total Voting Power: ${totalSupply} votes`);
    console.log(`Max Possible Voters: ${maxSupply} members`);
    console.log(`Votes Per Member: ${maxPerWallet} vote(s)`);
    
    if (maxPerWallet.eq(1)) {
      console.log('✅ Perfect for DAO: 1 NFT per wallet = 1 vote per member');
    }

    // Check if any NFTs are minted
    if (totalSupply.gt(0)) {
      console.log('\n👥 Current NFT Holders:');
      console.log('-'.repeat(25));
      
      for (let i = 1; i <= Math.min(totalSupply.toNumber(), 10); i++) {
        try {
          const tokenOwner = await we3NFT.ownerOf(i);
          const balance = await we3NFT.balanceOf(tokenOwner);
          console.log(`Token ${i}: ${tokenOwner} (${balance} NFT${balance.eq(1) ? '' : 's'})`);
        } catch (error) {
          console.log(`Token ${i}: Error reading owner`);
        }
      }
      
      if (totalSupply.gt(10)) {
        console.log(`... and ${totalSupply.sub(10)} more tokens`);
      }
    } else {
      console.log('\n📝 No NFTs minted yet');
    }

    // Voting plugin deployment readiness
    console.log('\n🚀 Voting Plugin Deployment Readiness:');
    console.log('-'.repeat(40));
    console.log('✅ Contract is ERC721 compliant');
    console.log('✅ All required functions available');
    console.log('✅ One vote per member design');
    console.log('✅ Ready for voting plugin integration');

    console.log('\n🎯 Next Steps:');
    console.log('1. Deploy Aragon voting plugin with this NFT contract');
    console.log('2. Set up DAO with the voting plugin');
    console.log('3. Test voting functionality');
    console.log('4. Create role management proposals');

    // Generate deployment command for voting plugin
    console.log('\n📝 Voting Plugin Deployment Command:');
    console.log('-'.repeat(40));
    console.log('cd aragon-nft-voting-plugin');
    console.log(`export NFT_CONTRACT_ADDRESS=${contractAddress}`);
    console.log('export NETWORK=polygon-amoy');
    console.log('npm run deploy -- --network polygon-amoy');

  } catch (error) {
    console.error('❌ Error accessing contract:', error);
    console.log('\nPossible issues:');
    console.log('1. Check RPC URL is correct');
    console.log('2. Verify private key has access');
    console.log('3. Ensure contract address is correct');
    console.log('4. Check network connection');
  }
}

// Execute verification
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Verification failed:', error);
      process.exit(1);
    });
}

export default main;
