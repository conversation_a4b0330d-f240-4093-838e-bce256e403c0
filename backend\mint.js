const { ethers } = require('ethers');
const fs = require('fs');
const path = require('path');

// Load Contract ABI from file or use default
let NFT_ABI;
try {
  const abiPath = path.join(__dirname, 'abi.json');
  if (fs.existsSync(abiPath)) {
    NFT_ABI = JSON.parse(fs.readFileSync(abiPath, 'utf8'));
    console.log('📄 Loaded contract ABI from abi.json');
  } else {
    // Fallback ABI for common ERC-721A functions
    NFT_ABI = [
      "function mint(address to) public returns (uint256)",
      "function mintTo(address to) public returns (uint256)",
      "function safeMint(address to, uint256 quantity) public payable",
      "function totalSupply() public view returns (uint256)",
      "function tokenURI(uint256 tokenId) public view returns (string)",
      "function ownerOf(uint256 tokenId) public view returns (address)",
      "function balanceOf(address owner) public view returns (uint256)",
      "function nextTokenId() public view returns (uint256)",
      "event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)"
    ];
    console.log('⚠️  Using fallback ABI - please provide abi.json for your deployed contract');
  }
} catch (error) {
  console.error('❌ Error loading ABI:', error.message);
  // Use minimal fallback ABI
  NFT_ABI = [
    "function mint(address to) public returns (uint256)",
    "function totalSupply() public view returns (uint256)",
    "event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)"
  ];
}

// Polygon Network Configuration
const config = {
  // Use Polygon mainnet or testnet
  rpcUrl: process.env.NODE_ENV === 'production'
    ? process.env.POLYGON_RPC
    : process.env.POLYGON_TESTNET_RPC || 'https://polygon-mumbai.infura.io/v3/YOUR_INFURA_KEY',
  contractAddress: process.env.CONTRACT_ADDRESS || '******************************************',
  privateKey: process.env.PRIVATE_KEY || '0x0000000000000000000000000000000000000000000000000000000000000000',
  gasLimit: process.env.GAS_LIMIT || '300000',
  gasPrice: process.env.GAS_PRICE || '30000000000', // 30 gwei for Polygon
  chainId: process.env.CHAIN_ID || 137 // Polygon mainnet
};

// Initialize provider and wallet
let provider, wallet, contract;
let isBlockchainEnabled = false;

// Async initialization function
async function initializeBlockchain() {
  try {
    // Check if we have valid configuration for real blockchain interaction
    const hasValidConfig = config.privateKey !== '0x0000000000000000000000000000000000000000000000000000000000000000'
      && config.contractAddress !== '******************************************'
      && (config.rpcUrl.includes('infura') || config.rpcUrl.includes('alchemy') || config.rpcUrl.includes('quicknode'));

    if (hasValidConfig) {
      provider = new ethers.JsonRpcProvider(config.rpcUrl);
      wallet = new ethers.Wallet(config.privateKey, provider);
      contract = new ethers.Contract(config.contractAddress, NFT_ABI, wallet);

      // Test connection
      await provider.getNetwork();
      isBlockchainEnabled = true;

      console.log('✅ Polygon blockchain connection initialized');
      console.log(`📍 Network: ${config.chainId === 137 ? 'Polygon Mainnet' : 'Polygon Mumbai Testnet'}`);
      console.log(`📄 Contract: ${config.contractAddress}`);
    } else {
      console.log('⚠️  Blockchain connection not configured');
      console.log('💡 Please configure POLYGON_RPC, CONTRACT_ADDRESS, and PRIVATE_KEY in .env file');
    }
  } catch (error) {
    console.error('❌ Error initializing blockchain connection:', error.message);
    console.log('⚠️  Falling back to demo mode');
  }
}

// Initialize blockchain connection (non-blocking)
initializeBlockchain();

/**
 * Mint NFT to specified wallet address
 * @param {string} toAddress - Recipient wallet address
 * @returns {Promise<{tokenId: number, transactionHash: string}>}
 */
async function mintNFT(toAddress) {
  try {
    console.log(`🎨 Minting NFT to address: ${toAddress}`);

    // Validate address
    if (!isValidWalletAddress(toAddress)) {
      throw new Error('Invalid wallet address format');
    }

    // Real blockchain minting if enabled
    if (isBlockchainEnabled && contract) {
      console.log('🔗 Using real Polygon blockchain minting...');

      try {
        // Detect available mint function
        let mintFunction, mintArgs;

        if (contract.mintTo) {
          mintFunction = 'mintTo';
          mintArgs = [toAddress];
        } else if (contract.mint) {
          // Check if mint function takes quantity parameter
          try {
            await contract.mint.estimateGas(toAddress, 1);
            mintFunction = 'mint';
            mintArgs = [toAddress, 1];
          } catch {
            // Try single parameter mint
            mintFunction = 'mint';
            mintArgs = [toAddress];
          }
        } else if (contract.safeMint) {
          mintFunction = 'safeMint';
          mintArgs = [toAddress, 1];
        } else {
          throw new Error('No supported mint function found in contract');
        }

        console.log(`🎯 Using mint function: ${mintFunction}(${mintArgs.join(', ')})`);

        // Get next token ID (if available)
        let nextTokenId;
        try {
          nextTokenId = await contract.nextTokenId();
          console.log(`📝 Next Token ID: ${nextTokenId}`);
        } catch {
          try {
            const totalSupply = await contract.totalSupply();
            nextTokenId = Number(totalSupply) + 1;
            console.log(`📝 Next Token ID (from totalSupply): ${nextTokenId}`);
          } catch {
            nextTokenId = 1;
            console.log(`📝 Using default Token ID: ${nextTokenId}`);
          }
        }

        // Estimate gas for the transaction
        const gasEstimate = await contract[mintFunction].estimateGas(...mintArgs);
        const gasLimit = Math.ceil(Number(gasEstimate) * 1.2); // Add 20% buffer

        console.log(`⛽ Estimated gas: ${gasEstimate}, Using: ${gasLimit}`);

        // Get current gas price from network
        const feeData = await provider.getFeeData();
        const gasPrice = feeData.gasPrice;

        console.log(`💰 Gas price: ${ethers.formatUnits(gasPrice, 'gwei')} gwei`);

        // Execute mint transaction
        const tx = await contract[mintFunction](...mintArgs, {
          gasLimit: gasLimit,
          gasPrice: gasPrice
        });

        console.log(`📤 Transaction sent: ${tx.hash}`);
        console.log(`⏳ Waiting for confirmation...`);

        // Wait for transaction confirmation
        const receipt = await tx.wait();

        if (receipt.status === 1) {
          // Extract token ID from Transfer event
          const transferEvent = receipt.logs
            .map(log => {
              try {
                return contract.interface.parseLog(log);
              } catch {
                return null;
              }
            })
            .find(log => log && log.name === 'Transfer');

          const tokenId = transferEvent ? Number(transferEvent.args.tokenId) : Number(nextTokenId);

          console.log(`✅ NFT minted successfully!`);
          console.log(`🎫 Token ID: ${tokenId}`);
          console.log(`🔗 Transaction: ${tx.hash}`);
          console.log(`⛽ Gas used: ${receipt.gasUsed}`);

          return {
            tokenId: tokenId,
            transactionHash: tx.hash,
            gasUsed: Number(receipt.gasUsed),
            blockNumber: receipt.blockNumber
          };
        } else {
          throw new Error('Transaction failed - status 0');
        }

      } catch (blockchainError) {
        console.error('❌ Blockchain minting failed:', blockchainError.message);

        // Handle specific blockchain errors
        if (blockchainError.code === 'INSUFFICIENT_FUNDS') {
          throw new Error('Insufficient funds for gas fees');
        } else if (blockchainError.code === 'NONCE_EXPIRED') {
          throw new Error('Transaction nonce expired, please try again');
        } else if (blockchainError.message.includes('revert')) {
          throw new Error('Smart contract rejected the transaction');
        } else {
          throw new Error(`Blockchain error: ${blockchainError.message}`);
        }
      }
    } else {
      // Blockchain not properly configured
      throw new Error('Blockchain connection not configured. Please check your environment variables.');
    }

  } catch (error) {
    console.error('❌ Error minting NFT:', error.message);
    throw new Error(`Minting failed: ${error.message}`);
  }
}

/**
 * Get NFT metadata
 * @param {number} tokenId - Token ID
 * @returns {Promise<object>} NFT metadata
 */
async function getNFTMetadata(tokenId) {
  // For demo purposes, return mock metadata
  return {
    name: `NFT Pass #${tokenId}`,
    description: `Exclusive NFT Pass #${tokenId} for early supporters`,
    image: `https://api.example.com/nft/${tokenId}/image.png`,
    attributes: [
      {
        trait_type: "Rarity",
        value: "Common"
      },
      {
        trait_type: "Generation",
        value: "Genesis"
      },
      {
        trait_type: "Minted Date",
        value: new Date().toISOString().split('T')[0]
      }
    ]
  };
}

/**
 * Validate wallet address format
 * @param {string} address - Wallet address to validate
 * @returns {boolean} True if valid Ethereum address
 */
function isValidWalletAddress(address) {
  // Basic validation for Ethereum address format
  if (!address || typeof address !== 'string') {
    return false;
  }

  // Check if it starts with 0x and has 40 hex characters
  const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
  return ethAddressRegex.test(address);
}

/**
 * Get contract information
 * @returns {Promise<object>} Contract details
 */
async function getContractInfo() {
  try {
    if (!isBlockchainEnabled || !contract) {
      return {
        isConnected: false,
        network: 'Disconnected',
        contractAddress: config.contractAddress,
        totalSupply: 0,
        error: 'Blockchain connection not configured'
      };
    }

    const totalSupply = await contract.totalSupply();
    const network = await provider.getNetwork();

    return {
      isConnected: true,
      network: network.name === 'matic' ? 'Polygon Mainnet' : 'Polygon Mumbai',
      chainId: Number(network.chainId),
      contractAddress: config.contractAddress,
      totalSupply: Number(totalSupply),
      nextTokenId: await contract.nextTokenId().catch(() => totalSupply + 1n)
    };
  } catch (error) {
    console.error('Error getting contract info:', error);
    return {
      isConnected: false,
      network: 'Error',
      contractAddress: config.contractAddress,
      totalSupply: 0,
      error: error.message
    };
  }
}

/**
 * Check if wallet has sufficient balance for gas
 * @param {string} walletAddress - Wallet to check
 * @returns {Promise<boolean>} True if sufficient balance
 */
async function checkWalletBalance(walletAddress) {
  try {
    if (!isBlockchainEnabled || !provider) {
      return true; // Skip check in demo mode
    }

    const balance = await provider.getBalance(walletAddress);
    const minBalance = ethers.parseEther('0.01'); // Minimum 0.01 MATIC

    return balance >= minBalance;
  } catch (error) {
    console.error('Error checking wallet balance:', error);
    return false;
  }
}

/**
 * Get transaction details
 * @param {string} txHash - Transaction hash
 * @returns {Promise<object>} Transaction details
 */
async function getTransactionDetails(txHash) {
  try {
    if (!isBlockchainEnabled || !provider) {
      return { status: 'demo', hash: txHash };
    }

    const tx = await provider.getTransaction(txHash);
    const receipt = await provider.getTransactionReceipt(txHash);

    return {
      hash: txHash,
      status: receipt ? (receipt.status === 1 ? 'success' : 'failed') : 'pending',
      blockNumber: receipt?.blockNumber,
      gasUsed: receipt ? Number(receipt.gasUsed) : null,
      from: tx?.from,
      to: tx?.to,
      value: tx ? ethers.formatEther(tx.value) : '0'
    };
  } catch (error) {
    console.error('Error getting transaction details:', error);
    return { status: 'error', hash: txHash, error: error.message };
  }
}

module.exports = {
  mintNFT,
  getNFTMetadata,
  isValidWalletAddress,
  getContractInfo,
  checkWalletBalance,
  getTransactionDetails,
  isBlockchainEnabled: () => isBlockchainEnabled
};
