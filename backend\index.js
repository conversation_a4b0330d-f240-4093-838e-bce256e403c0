const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB connection
console.log('Connecting to MongoDB...');
console.log('MONGO_URI:', process.env.MONGO_URI ? 'Found' : 'Not found');
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/nft-minting-platform');

mongoose.connection.on('connected', () => {
  console.log('Connected to MongoDB');
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
});

// Models
const Email = mongoose.model('Email', new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}));

const NftUser = require('./models/NftUser');
const {
  mintNFT,
  getNFTMetadata,
  isValidWalletAddress,
  getContractInfo,
  checkWalletBalance,
  getTransactionDetails,
  isBlockchainEnabled
} = require('./mint');

// Routes
app.post('/register-email', async (req, res) => {
  const { email } = req.body;

  // Validation
  if (!email || !email.includes('@')) {
    return res.status(400).send('Invalid email');
  }

  try {
    // Check if email already exists
    const existing = await Email.findOne({ email: email.toLowerCase() });
    if (existing) {
      return res.status(400).send('Email already registered');
    }

    // Create new email entry
    await Email.create({ email: email.toLowerCase() });
    res.send('Email registered successfully');
  } catch (err) {
    console.error('Error registering email:', err);
    res.status(500).send('Server error');
  }
});

// NFT Minting endpoint
app.post('/mint-nft', async (req, res) => {
  const { email, wallet } = req.body;

  // Input validation
  if (!email || !wallet) {
    return res.status(400).json({
      error: 'Missing required fields',
      details: 'Both email and wallet address are required'
    });
  }

  if (!email.includes('@')) {
    return res.status(400).json({
      error: 'Invalid email format',
      details: 'Please provide a valid email address'
    });
  }

  if (!isValidWalletAddress(wallet)) {
    return res.status(400).json({
      error: 'Invalid wallet address format',
      details: 'Wallet address must be a valid Ethereum address (0x...)'
    });
  }

  try {
    // Check if email or wallet already minted
    const existing = await NftUser.findOne({
      $or: [
        { email: email.toLowerCase() },
        { wallet: wallet.toLowerCase() }
      ]
    });

    if (existing) {
      const field = existing.email === email.toLowerCase() ? 'email' : 'wallet';
      return res.status(400).json({
        error: `Already minted`,
        details: `This ${field} has already been used to mint an NFT`,
        existingTokenId: existing.tokenId,
        existingTransaction: existing.transactionHash,
        mintedAt: existing.mintedAt
      });
    }

    // Check wallet balance for gas (if blockchain enabled)
    if (isBlockchainEnabled()) {
      const hasBalance = await checkWalletBalance(wallet);
      if (!hasBalance) {
        return res.status(400).json({
          error: 'Insufficient wallet balance',
          details: 'Wallet needs at least 0.01 MATIC for gas fees'
        });
      }
    }

    // Start minting process
    console.log(`🚀 Starting NFT mint for ${email} to wallet ${wallet}`);

    // Create pending record first
    const pendingRecord = await NftUser.create({
      email: email.toLowerCase(),
      wallet: wallet.toLowerCase(),
      tokenId: 0, // Will be updated after minting
      transactionHash: 'pending',
      status: 'pending'
    });

    try {
      // Mint NFT
      const mintResult = await mintNFT(wallet.toLowerCase());

      // Get metadata for the minted NFT
      const metadata = await getNFTMetadata(mintResult.tokenId);

      // Update record with successful mint
      const updatedRecord = await NftUser.findByIdAndUpdate(
        pendingRecord._id,
        {
          tokenId: mintResult.tokenId,
          transactionHash: mintResult.transactionHash,
          status: 'minted',
          metadata: metadata,
          mintedAt: new Date()
        },
        { new: true }
      );

      console.log(`✅ NFT minted successfully for ${email}. TokenId: ${mintResult.tokenId}`);

      res.json({
        success: true,
        message: 'NFT minted successfully on Polygon!',
        tokenId: mintResult.tokenId,
        transactionHash: mintResult.transactionHash,
        metadata: metadata,
        mintedAt: updatedRecord.mintedAt,
        gasUsed: mintResult.gasUsed,
        blockNumber: mintResult.blockNumber,
        network: 'Polygon'
      });

    } catch (mintError) {
      // Update record with failed status
      await NftUser.findByIdAndUpdate(
        pendingRecord._id,
        {
          status: 'failed',
          transactionHash: 'failed',
          metadata: { error: mintError.message }
        }
      );

      throw mintError; // Re-throw to be caught by outer catch
    }

  } catch (err) {
    console.error('❌ Error minting NFT:', err.message);

    // Determine error type and provide appropriate response
    let errorResponse = {
      success: false,
      error: 'NFT minting failed',
      details: err.message
    };

    // Handle specific error types
    if (err.message.includes('Insufficient funds')) {
      errorResponse.error = 'Insufficient funds for gas fees';
      errorResponse.details = 'Please ensure your wallet has enough MATIC for transaction fees';
    } else if (err.message.includes('revert')) {
      errorResponse.error = 'Smart contract error';
      errorResponse.details = 'The smart contract rejected the transaction';
    } else if (err.message.includes('network')) {
      errorResponse.error = 'Network error';
      errorResponse.details = 'Unable to connect to Polygon network';
    }

    res.status(500).json(errorResponse);
  }
});

// Get NFT info by email or wallet
app.get('/nft-info', async (req, res) => {
  const { email, wallet } = req.query;

  if (!email && !wallet) {
    return res.status(400).json({ error: 'Email or wallet address required' });
  }

  try {
    const query = {};
    if (email) query.email = email.toLowerCase();
    if (wallet) query.wallet = wallet.toLowerCase();

    const nftUser = await NftUser.findOne(query);

    if (!nftUser) {
      return res.status(404).json({ error: 'No NFT found for this email/wallet' });
    }

    res.json({
      email: nftUser.email,
      wallet: nftUser.wallet,
      tokenId: nftUser.tokenId,
      transactionHash: nftUser.transactionHash,
      status: nftUser.status,
      metadata: nftUser.metadata,
      mintedAt: nftUser.mintedAt
    });

  } catch (err) {
    console.error('Error fetching NFT info:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get minting statistics
app.get('/mint-stats', async (req, res) => {
  try {
    const totalMinted = await NftUser.countDocuments({ status: 'minted' });
    const totalFailed = await NftUser.countDocuments({ status: 'failed' });
    const totalPending = await NftUser.countDocuments({ status: 'pending' });

    res.json({
      totalMinted,
      totalFailed,
      totalPending,
      totalAttempts: totalMinted + totalFailed + totalPending
    });
  } catch (err) {
    console.error('Error fetching mint stats:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get contract information
app.get('/contract-info', async (req, res) => {
  try {
    const contractInfo = await getContractInfo();
    res.json({
      success: true,
      ...contractInfo,
      mode: isBlockchainEnabled() ? 'blockchain' : 'demo',
      readyForProduction: isBlockchainEnabled() && contractInfo.isConnected,
      setupInstructions: !isBlockchainEnabled() ?
        'Please configure POLYGON_RPC, CONTRACT_ADDRESS, and PRIVATE_KEY in .env file' :
        null
    });
  } catch (err) {
    console.error('Error fetching contract info:', err);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch contract information',
      details: err.message
    });
  }
});

// Validate contract setup
app.get('/validate-contract', async (req, res) => {
  try {
    const validation = {
      hasContractAddress: !!process.env.CONTRACT_ADDRESS && process.env.CONTRACT_ADDRESS !== '0x1234567890123456789012345678901234567890',
      hasRpcUrl: !!process.env.POLYGON_RPC || !!process.env.POLYGON_TESTNET_RPC,
      hasPrivateKey: !!process.env.PRIVATE_KEY && process.env.PRIVATE_KEY !== '0x0000000000000000000000000000000000000000000000000000000000000000',
      blockchainEnabled: isBlockchainEnabled(),
      abiLoaded: true // ABI is always loaded (from file or fallback)
    };

    const allValid = Object.values(validation).every(v => v === true);

    res.json({
      success: true,
      validation,
      isReady: allValid,
      nextSteps: allValid ?
        ['Contract is ready for production minting!'] :
        [
          !validation.hasContractAddress && 'Set CONTRACT_ADDRESS in .env',
          !validation.hasRpcUrl && 'Set POLYGON_RPC in .env',
          !validation.hasPrivateKey && 'Set PRIVATE_KEY in .env',
          'Restart the server after updating .env'
        ].filter(Boolean)
    });
  } catch (err) {
    console.error('Error validating contract setup:', err);
    res.status(500).json({
      success: false,
      error: 'Failed to validate contract setup',
      details: err.message
    });
  }
});

// Get transaction details
app.get('/transaction/:hash', async (req, res) => {
  const { hash } = req.params;

  if (!hash) {
    return res.status(400).json({
      success: false,
      error: 'Transaction hash required'
    });
  }

  try {
    const txDetails = await getTransactionDetails(hash);
    res.json({
      success: true,
      transaction: txDetails
    });
  } catch (err) {
    console.error('Error fetching transaction details:', err);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch transaction details',
      details: err.message
    });
  }
});


// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'NFT Minting Platform is running',
    blockchain: isBlockchainEnabled() ? 'Connected to Polygon' : 'Disconnected',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});