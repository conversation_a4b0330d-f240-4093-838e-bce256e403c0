# =============================================================================
# BLOCKCHAIN CONFIGURATION
# =============================================================================

# Wallet Configuration
MNEMONIC="test test test test test test test test test test test junk"
PRIVATE_KEY=your_private_key_here

# RPC Provider Configuration
INFURA_API_KEY=your_infura_api_key_here
ALCHEMY_API_KEY=your_alchemy_api_key_here

# Block Explorer API Keys
ETHERSCAN_API_KEY=your_etherscan_api_key_here
POLYGONSCAN_API_KEY=your_polygonscan_api_key_here
ARBISCAN_API_KEY=your_arbiscan_api_key_here
BASESCAN_API_KEY=your_basescan_api_key_here
OPTIMISM_API_KEY=your_optimism_api_key_here

# Gas Configuration
GAS_PRICE=auto
GAS_LIMIT=auto
REPORT_GAS=false
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_for_gas_reporting

# =============================================================================
# NFT CONTRACT CONFIGURATION
# =============================================================================

# WE3HomeNFT Configuration
NFT_BASE_URI=https://api.we3home.com/metadata/
BACKEND_ADDRESS=******************************************

# Contract Addresses (filled after deployment)
WE3HOME_NFT_ADDRESS=
VOTING_PLUGIN_ADDRESS=
DAO_ADDRESS=

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Deployment Options
DEPLOY_PLUGIN=true
MINT_TEST_NFTS=true
VERIFY_CONTRACTS=true

# Test Configuration
TEST_ADDRESSES=******************************************,******************************************

# =============================================================================
# ARAGON PLUGIN CONFIGURATION
# =============================================================================

# Voting Settings
VOTING_MODE=0
SUPPORT_THRESHOLD=500000
MIN_PARTICIPATION=250000
MIN_DURATION=86400
MIN_PROPOSER_VOTING_POWER=1

# Plugin Repository
PLUGIN_REPO_ENS_NAME=nft-voting-plugin
VERSION_RELEASE=1
VERSION_BUILD=1

# Role Configuration
MANAGER_ROLE=0x241ecf16d79d0f8dbfb92cbc07fe17840425976cf0667f022fe9877caa831b08
COUNCIL_ROLE=0x71840dc4906352362b0cdaf79870196c8e42acafade72d5d5a6d59291253ceb1

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================

# Database
MONGODB_URI=mongodb://localhost:27017/nft-platform
DB_NAME=nft_platform

# Server Configuration
PORT=3001
NODE_ENV=development

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Next.js Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_CHAIN_ID=11155111
NEXT_PUBLIC_NETWORK_NAME=sepolia

# Contract Addresses (for frontend)
NEXT_PUBLIC_NFT_CONTRACT_ADDRESS=
NEXT_PUBLIC_VOTING_PLUGIN_ADDRESS=

# Web3 Configuration
NEXT_PUBLIC_INFURA_ID=your_infura_project_id
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Test Network Configuration
TEST_NETWORK=sepolia
TEST_MNEMONIC="test test test test test test test test test test test junk"

# Test Accounts (for integration testing)
TEST_ALICE_ADDRESS=******************************************
TEST_BOB_ADDRESS=******************************************
TEST_CHARLIE_ADDRESS=******************************************

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Error Tracking
SENTRY_DSN=your_sentry_dsn_here

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_id_here

# Monitoring
WEBHOOK_URL=your_discord_or_slack_webhook_for_notifications

# =============================================================================
# SECURITY
# =============================================================================

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# API Keys
API_KEY_SECRET=your_api_key_secret_here

# =============================================================================
# DEVELOPMENT
# =============================================================================

# Debug Configuration
DEBUG=false
VERBOSE_LOGGING=false

# Development Tools
HOT_RELOAD=true
SOURCE_MAPS=true

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================

# Production Database
# MONGODB_URI=mongodb+srv://username:<EMAIL>/production_db

# Production API
# NEXT_PUBLIC_API_URL=https://api.we3home.com

# Production Contracts (Mainnet)
# NEXT_PUBLIC_CHAIN_ID=1
# NEXT_PUBLIC_NETWORK_NAME=mainnet
