import '@nomicfoundation/hardhat-chai-matchers';
import '@nomicfoundation/hardhat-network-helpers';
import '@nomicfoundation/hardhat-verify';
import '@nomiclabs/hardhat-ethers';
import '@openzeppelin/hardhat-upgrades';
import '@typechain/hardhat';
import 'hardhat-deploy';
import 'hardhat-gas-reporter';
import 'solidity-coverage';

import {HardhatUserConfig} from 'hardhat/config';
import {NetworkUserConfig} from 'hardhat/types';

import * as dotenv from 'dotenv';

dotenv.config();

// Ensure that we have all the environment variables we need.
const mnemonic: string | undefined = process.env.MNEMONIC;
if (!mnemonic) {
  throw new Error('Please set your MNEMONIC in a .env file');
}

const infuraApiKey: string | undefined = process.env.INFURA_API_KEY;
if (!infuraApiKey) {
  throw new Error('Please set your INFURA_API_KEY in a .env file');
}

function getChainConfig(chain: keyof typeof chainIds): NetworkUserConfig {
  let jsonRpcUrl: string;
  switch (chain) {
    case 'avalanche':
      jsonRpcUrl = 'https://api.avax.network/ext/bc/C/rpc';
      break;
    case 'bsc':
      jsonRpcUrl = 'https://bsc-dataseed1.binance.org';
      break;
    case 'mainnet':
      jsonRpcUrl = 'https://mainnet.infura.io/v3/' + infuraApiKey;
      break;
    case 'polygon':
      jsonRpcUrl = 'https://polygon-mainnet.infura.io/v3/' + infuraApiKey;
      break;
    case 'sepolia':
      jsonRpcUrl = 'https://sepolia.infura.io/v3/' + infuraApiKey;
      break;
    case 'polygonMumbai':
      jsonRpcUrl = 'https://polygon-mumbai.infura.io/v3/' + infuraApiKey;
      break;
    case 'polygonAmoy':
      jsonRpcUrl = process.env.POLYGON_TESTNET_RPC || 'https://polygon-amoy.infura.io/v3/' + infuraApiKey;
      break;
    default:
      jsonRpcUrl = 'https://' + chain + '.infura.io/v3/' + infuraApiKey;
  }
  return {
    accounts: {
      count: 10,
      mnemonic,
      path: "m/44'/60'/0'/0",
    },
    chainId: chainIds[chain],
    url: jsonRpcUrl,
  };
}

const chainIds = {
  avalanche: 43114,
  bsc: 56,
  hardhat: 31337,
  mainnet: 1,
  polygon: 137,
  sepolia: ********,
  polygonMumbai: 80001,
  polygonAmoy: 80002,
};

const config: HardhatUserConfig = {
  defaultNetwork: 'hardhat',
  namedAccounts: {
    deployer: 0,
  },
  etherscan: {
    apiKey: {
      avalanche: process.env.SNOWTRACE_API_KEY || '',
      bsc: process.env.BSCSCAN_API_KEY || '',
      mainnet: process.env.ETHERSCAN_API_KEY || '',
      polygon: process.env.POLYGONSCAN_API_KEY || '',
      sepolia: process.env.ETHERSCAN_API_KEY || '',
      polygonMumbai: process.env.POLYGONSCAN_API_KEY || '',
    },
  },
  gasReporter: {
    currency: 'USD',
    enabled: process.env.REPORT_GAS ? true : false,
    excludeContracts: [],
    src: './contracts',
  },
  networks: {
    hardhat: {
      accounts: {
        mnemonic,
      },
      chainId: chainIds.hardhat,
      forking: {
        url: 'https://mainnet.infura.io/v3/' + infuraApiKey,
        blockNumber: ********,
      },
    },
    avalanche: getChainConfig('avalanche'),
    bsc: getChainConfig('bsc'),
    mainnet: getChainConfig('mainnet'),
    polygon: getChainConfig('polygon'),
    sepolia: getChainConfig('sepolia'),
    polygonMumbai: getChainConfig('polygonMumbai'),
    polygonAmoy: getChainConfig('polygonAmoy'),
  },
  paths: {
    artifacts: './artifacts',
    cache: './cache',
    sources: './contracts',
    tests: './test',
  },
  solidity: {
    version: '0.8.17',
    settings: {
      metadata: {
        // Not including the metadata hash
        // https://github.com/paulrberg/hardhat-template/issues/31
        bytecodeHash: 'none',
      },
      // Disable the optimizer when debugging
      // https://hardhat.org/hardhat-network/#solidity-optimizer-support
      optimizer: {
        enabled: true,
        runs: 800,
      },
    },
  },
  typechain: {
    outDir: 'typechain',
    target: 'ethers-v5',
  },
};

export default config;
