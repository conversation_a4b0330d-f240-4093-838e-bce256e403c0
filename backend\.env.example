# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority

# Server Configuration
PORT=5000
NODE_ENV=development

# Blockchain Configuration - Polygon Network
# Polygon Mainnet
POLYGON_RPC=https://polygon-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
# Polygon Mumbai Testnet (for testing)
POLYGON_TESTNET_RPC=https://polygon-mumbai.infura.io/v3/YOUR_INFURA_PROJECT_ID
CHAIN_ID=137

# Smart Contract Configuration
CONTRACT_ADDRESS=0x1234567890123456789012345678901234567890
PRIVATE_KEY=0x0000000000000000000000000000000000000000000000000000000000000000

# Alternative RPC Providers
# ALCHEMY_POLYGON_RPC=https://polygon-mainnet.g.alchemy.com/v2/YOUR_API_KEY
# QUICKNODE_RPC=https://your-endpoint.polygon.quiknode.pro/YOUR_API_KEY/

# Gas Configuration
GAS_LIMIT=300000
GAS_PRICE=20000000000

# NFT Metadata Configuration
METADATA_BASE_URI=https://api.yourproject.com/metadata/
IMAGE_BASE_URI=https://api.yourproject.com/images/

# Security
JWT_SECRET=your-super-secret-jwt-key
API_RATE_LIMIT=100

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Development Flags
DEMO_MODE=true
ENABLE_LOGGING=true
DEBUG_BLOCKCHAIN=false
