env:
  browser: false
  es6: true
  mocha: true
  node: true
extends:
  - '@typescript-eslint/recommended'
  - 'eslint:recommended'
  - 'prettier'
globals:
  Atomics: readonly
  SharedArrayBuffer: readonly
parser: '@typescript-eslint/parser'
parserOptions:
  ecmaVersion: 2020
  sourceType: module
plugins:
  - '@typescript-eslint'
rules:
  '@typescript-eslint/no-unused-vars': 'error'
  '@typescript-eslint/no-explicit-any': 'off'
  'prefer-const': 'error'
