import {ethers} from 'hardhat';

async function main() {
  console.log('🚀 Deploying Simple NFT Voting Contract');
  console.log('='.repeat(50));

  // Your deployed WE3HomeNFT contract address
  const nftContractAddress = '******************************************';
  
  const [deployer] = await ethers.getSigners();
  console.log('Deploying with account:', deployer.address);
  console.log('Account balance:', ethers.utils.formatEther(await deployer.getBalance()), 'MATIC');

  // Get network information
  const network = await ethers.provider.getNetwork();
  console.log('Network:', network.name);
  console.log('Chain ID:', network.chainId);

  console.log('\n📋 Deployment Configuration:');
  console.log('='.repeat(40));
  console.log(`NFT Contract: ${nftContractAddress}`);
  console.log(`Deployer: ${deployer.address}`);
  console.log('='.repeat(40));

  // Verify NFT contract is accessible
  console.log('\n🔍 Verifying NFT Contract...');
  try {
    const nftContract = await ethers.getContractAt('WE3HomeNFT', nftContractAddress);
    const name = await nftContract.name();
    const symbol = await nftContract.symbol();
    const totalSupply = await nftContract.totalSupply();
    
    console.log(`✅ NFT Contract verified:`);
    console.log(`   Name: ${name}`);
    console.log(`   Symbol: ${symbol}`);
    console.log(`   Total Supply: ${totalSupply}`);
  } catch (error) {
    console.error('❌ Error accessing NFT contract:', error);
    process.exit(1);
  }

  // Deploy Simple NFT Voting contract
  console.log('\n📦 Deploying SimpleNftVoting...');
  const SimpleNftVotingFactory = await ethers.getContractFactory('SimpleNftVoting');
  
  // Estimate gas
  const deploymentData = SimpleNftVotingFactory.getDeployTransaction(nftContractAddress);
  const gasEstimate = await deployer.estimateGas(deploymentData);
  console.log(`Estimated gas: ${gasEstimate.toString()}`);

  // Deploy with gas buffer
  const voting = await SimpleNftVotingFactory.deploy(nftContractAddress, {
    gasLimit: gasEstimate.mul(120).div(100) // 20% buffer
  });
  
  console.log('Deployment transaction sent...');
  await voting.deployed();

  console.log(`✅ SimpleNftVoting deployed at: ${voting.address}`);
  console.log('Gas used:', (await voting.deployTransaction.wait()).gasUsed.toString());

  // Verify deployment
  console.log('\n🔍 Verifying deployment...');
  const deployedNftAddress = await voting.nftContract();
  const owner = await voting.owner();
  const proposalCounter = await voting.proposalCounter();
  const votingDuration = await voting.votingDuration();
  const supportThreshold = await voting.supportThreshold();
  const minParticipation = await voting.minParticipation();

  console.log(`NFT Contract: ${deployedNftAddress}`);
  console.log(`Owner: ${owner}`);
  console.log(`Proposal Counter: ${proposalCounter}`);
  console.log(`Voting Duration: ${votingDuration.toString()} seconds (${votingDuration.div(86400)} days)`);
  console.log(`Support Threshold: ${supportThreshold.toString()} (${supportThreshold.div(10000)}%)`);
  console.log(`Min Participation: ${minParticipation.toString()} (${minParticipation.div(10000)}%)`);

  // Test basic functionality
  console.log('\n🧪 Testing basic functionality...');
  try {
    const totalVotingPower = await voting.getTotalVotingPower();
    console.log(`Total Voting Power: ${totalVotingPower}`);
    console.log('✅ Contract is functional!');
  } catch (error) {
    console.log('⚠️  Warning: Could not get total voting power:', error);
  }

  // Save deployment information
  const deploymentInfo = {
    network: {
      name: network.name,
      chainId: network.chainId,
    },
    contracts: {
      nftContract: nftContractAddress,
      votingContract: voting.address,
    },
    deployer: deployer.address,
    transactionHash: voting.deployTransaction.hash,
    timestamp: new Date().toISOString(),
    configuration: {
      votingDuration: votingDuration.toString(),
      supportThreshold: supportThreshold.toString(),
      minParticipation: minParticipation.toString(),
    }
  };

  // Write deployment info to file
  const fs = require('fs');
  const path = require('path');
  
  const deploymentsDir = path.join(__dirname, '..', 'deployments');
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, {recursive: true});
  }

  const deploymentFile = path.join(
    deploymentsDir,
    `simple-voting-${network.name}-${Date.now()}.json`
  );
  
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`\n💾 Deployment info saved to: ${deploymentFile}`);

  // Display usage instructions
  console.log('\n🎯 How to Use Your Voting Contract:');
  console.log('='.repeat(50));
  console.log('1. Create proposals:');
  console.log(`   voting.createProposal("Your proposal description")`);
  console.log('2. Vote with NFTs:');
  console.log(`   voting.vote(proposalId, tokenId, voteOption)`);
  console.log('   Vote options: 1=Abstain, 2=Yes, 3=No');
  console.log('3. Execute passed proposals:');
  console.log(`   voting.executeProposal(proposalId)`);

  console.log('\n📝 Example Commands:');
  console.log(`# Connect to your contract:`);
  console.log(`npx hardhat console --network ${network.name}`);
  console.log(`const voting = await ethers.getContractAt("SimpleNftVoting", "${voting.address}");`);
  console.log(`const nft = await ethers.getContractAt("WE3HomeNFT", "${nftContractAddress}");`);

  if (network.chainId !== 31337) { // Not local hardhat network
    console.log('\n📚 Verification Command:');
    console.log(`npx hardhat verify --network ${network.name} ${voting.address} "${nftContractAddress}"`);
  }

  console.log('\n🎉 Deployment Complete!');
  console.log('Your DAO members can now vote using their WE3Home NFTs!');

  return {
    votingContract: voting.address,
    nftContract: nftContractAddress,
    network: network.name,
    chainId: network.chainId,
  };
}

// Execute deployment if this script is run directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Deployment failed:', error);
      process.exit(1);
    });
}

export default main;
