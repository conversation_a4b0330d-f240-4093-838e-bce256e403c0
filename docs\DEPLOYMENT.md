# Deployment Guide

This guide covers deploying the NFT Minting Platform to production environments.

## 📋 Pre-deployment Checklist

- [ ] Environment variables configured
- [ ] Smart contracts deployed and verified
- [ ] Database setup and secured
- [ ] Domain and SSL certificates ready
- [ ] Monitoring and logging configured

## 🌐 Frontend Deployment (Vercel)

### 1. Prepare for Deployment
```bash
cd frontend
npm run build
npm run lint
```

### 2. Deploy to Vercel
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### 3. Environment Variables
Set in Vercel dashboard:
```env
NEXT_PUBLIC_API_URL=https://your-api-domain.com
NEXT_PUBLIC_CHAIN_ID=1
NEXT_PUBLIC_CONTRACT_ADDRESS=0x...
```

## 🚀 Backend Deployment (Railway)

### 1. Prepare Backend
```bash
cd backend
npm run test
npm run lint
```

### 2. Deploy to Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway deploy
```

### 3. Environment Variables
Set in Railway dashboard:
```env
NODE_ENV=production
MONGO_URI=mongodb+srv://...
RPC_URL=https://mainnet.infura.io/v3/...
CONTRACT_ADDRESS=0x...
CONTRACT_OWNER_PRIVATE_KEY=0x...
PORT=5000
```

## 🔗 Smart Contract Deployment

### 1. Setup Hardhat Config
```javascript
// hardhat.config.js
require("@nomicfoundation/hardhat-toolbox");

module.exports = {
  solidity: "0.8.19",
  networks: {
    mainnet: {
      url: process.env.RPC_URL,
      accounts: [process.env.PRIVATE_KEY]
    }
  },
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY
  }
};
```

### 2. Deploy Contract
```bash
cd backend
npm run compile
npm run deploy --network mainnet
```

### 3. Verify Contract
```bash
npm run verify --network mainnet DEPLOYED_CONTRACT_ADDRESS
```

## 🗄️ Database Setup (MongoDB Atlas)

### 1. Create Production Cluster
- Choose appropriate tier (M10+ for production)
- Enable backup
- Configure network access
- Create database user

### 2. Security Configuration
```javascript
// IP Whitelist: Add your server IPs
// Database Access: Create production user with minimal permissions
// Network Access: Configure VPC peering if needed
```

### 3. Connection String
```env
MONGO_URI=mongodb+srv://prod-user:<EMAIL>/nft-platform?retryWrites=true&w=majority
```

## 🔒 Security Considerations

### Environment Variables
- Never commit `.env` files
- Use secure secret management
- Rotate keys regularly
- Use different keys for different environments

### Smart Contract Security
- Audit contracts before mainnet deployment
- Use multi-sig wallets for contract ownership
- Implement proper access controls
- Monitor contract interactions

### API Security
- Implement rate limiting
- Use HTTPS only
- Validate all inputs
- Log security events

## 📊 Monitoring & Logging

### Application Monitoring
```javascript
// Add to backend/index.js
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

### Blockchain Monitoring
- Monitor contract events
- Track gas usage
- Alert on failed transactions
- Monitor wallet balances

## 🚨 Incident Response

### Backup Strategy
- Database: Automated daily backups
- Code: Git repository with tags
- Environment: Document all configurations

### Rollback Plan
1. Identify issue
2. Stop new deployments
3. Rollback to previous version
4. Verify functionality
5. Investigate root cause

## 📈 Performance Optimization

### Frontend
- Enable CDN
- Optimize images
- Implement caching
- Monitor Core Web Vitals

### Backend
- Database indexing
- API response caching
- Connection pooling
- Load balancing

### Blockchain
- Optimize gas usage
- Batch operations
- Monitor network congestion
- Implement retry logic

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm run install-all
      - name: Run tests
        run: npm test
      - name: Deploy
        run: npm run deploy
```

## 📞 Support & Maintenance

### Regular Tasks
- Monitor application health
- Update dependencies
- Review security logs
- Backup verification
- Performance analysis

### Emergency Contacts
- DevOps team
- Smart contract auditor
- Database administrator
- Security team

---

For additional support, refer to the [troubleshooting guide](TROUBLESHOOTING.md) or contact the development team.
