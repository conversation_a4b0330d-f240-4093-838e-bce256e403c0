// SPDX-License-Identifier: AGPL-3.0-or-later
pragma solidity 0.8.17;

import {<PERSON><PERSON><PERSON>} from "@aragon/osx/core/dao/IDAO.sol";
import {PermissionLib} from "@aragon/osx/core/permission/PermissionLib.sol";

/// @title MockDAO
/// <AUTHOR>
/// @notice Mock DAO contract for testing the NFT Voting Plugin
/// @dev This contract implements a simplified version of IDAO for testing purposes
contract MockDAO is IDAO {
    /// @notice The DAO's native token address
    address public override nativeToken;

    /// @notice Mapping to store granted permissions
    mapping(bytes32 => bool) public permissions;

    /// @notice Mapping to store role assignments
    mapping(bytes32 => mapping(address => bool)) public roles;

    /// @notice Array to store executed actions for testing
    Action[] public executedActions;

    /// @notice Event emitted when an action is executed
    event ActionExecuted(address indexed to, uint256 value, bytes data);

    /// @notice Event emitted when a permission is granted
    event PermissionGranted(address indexed where, address indexed who, bytes32 indexed permissionId);

    /// @notice Event emitted when a permission is revoked
    event PermissionRevoked(address indexed where, address indexed who, bytes32 indexed permissionId);

    /// @notice Event emitted when a role is granted
    event RoleGranted(bytes32 indexed role, address indexed account);

    /// @notice Event emitted when a role is revoked
    event RoleRevoked(bytes32 indexed role, address indexed account);

    /// @notice Constructor
    constructor() {
        nativeToken = address(0); // No native token for testing
    }

    /// @inheritdoc IDAO
    function execute(
        bytes32 _callId,
        Action[] calldata _actions,
        uint256 _allowFailureMap
    ) external override returns (bytes[] memory execResults, uint256 failureMap) {
        execResults = new bytes[](_actions.length);
        failureMap = 0;

        for (uint256 i = 0; i < _actions.length; i++) {
            // Store executed action for testing verification
            executedActions.push(_actions[i]);

            // Emit event for testing
            emit ActionExecuted(_actions[i].to, _actions[i].value, _actions[i].data);

            // Simulate execution
            if (_allowFailureMap & (1 << i) == 0) {
                // Action must succeed
                (bool success, bytes memory result) = _actions[i].to.call{value: _actions[i].value}(_actions[i].data);
                if (!success) {
                    failureMap |= (1 << i);
                }
                execResults[i] = result;
            } else {
                // Action can fail
                try this.executeAction(_actions[i]) returns (bytes memory result) {
                    execResults[i] = result;
                } catch {
                    failureMap |= (1 << i);
                    execResults[i] = "";
                }
            }
        }

        // Silence unused parameter warning
        _callId;
    }

    /// @notice Helper function to execute a single action
    /// @param _action The action to execute
    /// @return result The result of the action execution
    function executeAction(Action calldata _action) external returns (bytes memory result) {
        (bool success, bytes memory data) = _action.to.call{value: _action.value}(_action.data);
        require(success, "Action execution failed");
        return data;
    }

    /// @inheritdoc IDAO
    function deposit(
        address _token,
        uint256 _amount,
        string calldata _reference
    ) external payable override {
        // Simple implementation for testing
        // In a real DAO, this would handle token deposits
        
        // Silence unused parameter warnings
        (_token, _amount, _reference);
    }

    /// @inheritdoc IDAO
    function setMetadata(bytes calldata _metadata) external override {
        // Simple implementation for testing
        // In a real DAO, this would update metadata
        
        // Silence unused parameter warning
        _metadata;
    }

    /// @inheritdoc IDAO
    function setDaoURI(string calldata _daoURI) external override {
        // Simple implementation for testing
        // In a real DAO, this would update the DAO URI
        
        // Silence unused parameter warning
        _daoURI;
    }

    /// @notice Grants a permission (simplified for testing)
    /// @param _where The contract address where the permission is granted
    /// @param _who The address receiving the permission
    /// @param _permissionId The permission identifier
    function grant(address _where, address _who, bytes32 _permissionId) external {
        bytes32 permissionHash = keccak256(abi.encodePacked(_where, _who, _permissionId));
        permissions[permissionHash] = true;
        emit PermissionGranted(_where, _who, _permissionId);
    }

    /// @notice Revokes a permission (simplified for testing)
    /// @param _where The contract address where the permission is revoked
    /// @param _who The address losing the permission
    /// @param _permissionId The permission identifier
    function revoke(address _where, address _who, bytes32 _permissionId) external {
        bytes32 permissionHash = keccak256(abi.encodePacked(_where, _who, _permissionId));
        permissions[permissionHash] = false;
        emit PermissionRevoked(_where, _who, _permissionId);
    }

    /// @notice Checks if a permission is granted (simplified for testing)
    /// @param _where The contract address
    /// @param _who The address to check
    /// @param _permissionId The permission identifier
    /// @return True if the permission is granted
    function hasPermission(address _where, address _who, bytes32 _permissionId) external view returns (bool) {
        bytes32 permissionHash = keccak256(abi.encodePacked(_where, _who, _permissionId));
        return permissions[permissionHash];
    }

    /// @notice Grants a role (for testing role-based proposals)
    /// @param _role The role identifier
    /// @param _account The account to grant the role to
    function grantRole(bytes32 _role, address _account) external {
        roles[_role][_account] = true;
        emit RoleGranted(_role, _account);
    }

    /// @notice Revokes a role (for testing role-based proposals)
    /// @param _role The role identifier
    /// @param _account The account to revoke the role from
    function revokeRole(bytes32 _role, address _account) external {
        roles[_role][_account] = false;
        emit RoleRevoked(_role, _account);
    }

    /// @notice Checks if an account has a role
    /// @param _role The role identifier
    /// @param _account The account to check
    /// @return True if the account has the role
    function hasRole(bytes32 _role, address _account) external view returns (bool) {
        return roles[_role][_account];
    }

    /// @notice Returns the number of executed actions (for testing)
    /// @return The number of executed actions
    function getExecutedActionsCount() external view returns (uint256) {
        return executedActions.length;
    }

    /// @notice Returns an executed action by index (for testing)
    /// @param _index The index of the action
    /// @return The executed action
    function getExecutedAction(uint256 _index) external view returns (Action memory) {
        require(_index < executedActions.length, "Index out of bounds");
        return executedActions[_index];
    }

    /// @notice Clears executed actions (for testing)
    function clearExecutedActions() external {
        delete executedActions;
    }

    /// @notice Receives Ether
    receive() external payable {}

    /// @notice Fallback function
    fallback() external payable {}

    /// @notice Returns the balance of the DAO
    /// @return The balance in wei
    function getBalance() external view returns (uint256) {
        return address(this).balance;
    }

    /// @notice Withdraws Ether from the DAO (for testing)
    /// @param _to The address to send Ether to
    /// @param _amount The amount to withdraw
    function withdraw(address payable _to, uint256 _amount) external {
        require(address(this).balance >= _amount, "Insufficient balance");
        _to.transfer(_amount);
    }

    /// @notice Emergency function to recover stuck tokens (for testing)
    /// @param _token The token contract address
    /// @param _to The address to send tokens to
    /// @param _amount The amount to recover
    function recoverToken(address _token, address _to, uint256 _amount) external {
        IERC20(_token).transfer(_to, _amount);
    }
}

// Import IERC20 for token recovery
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
