# 🎨 NFT Minting Platform

A complete full-stack NFT minting platform with email registration, smart contract integration, and modern UI. Built with Next.js, Node.js, MongoDB, and Ethereum blockchain integration.

## 📋 Table of Contents
- [Features](#-features)
- [Tech Stack](#️-tech-stack)
- [Project Structure](#-project-structure)
- [Quick Start](#-quick-start)
- [Development Setup](#-development-setup)
- [API Documentation](#-api-documentation)
- [Smart Contract Integration](#-smart-contract-integration)
- [Environment Configuration](#-environment-configuration)
- [Testing](#-testing)
- [Deployment](#-deployment)
- [Contributing](#-contributing)

## 🚀 Features

### Phase 1: Landing Page & Email Registration ✅
- **Responsive landing page** with modern UI
- **Email registration** with validation
- **Privacy policy agreement** requirement
- **MongoDB integration** for data persistence
- **Success/error messaging**

### Phase 2: NFT Minting System ✅
- **Wallet address validation** (Ethereum format)
- **NFT minting endpoint** with smart contract integration
- **Duplicate prevention** (email and wallet)
- **NFT metadata generation** with attributes
- **Minting statistics** and tracking
- **Demo mode** for testing without blockchain

## 🛠️ Tech Stack

**Frontend:**
- Next.js 15 with TypeScript
- React 19
- Tailwind CSS
- Responsive design

**Backend:**
- Node.js with Express
- MongoDB with Mongoose
- Ethers.js for blockchain interaction
- CORS enabled

**Database:**
- MongoDB Atlas (cloud)
- Email collection for registrations
- NftUser collection for minted NFTs

## 📁 Project Structure

```
nft-minting-platform/
├── frontend/
│   ├── app/
│   │   ├── page.tsx          # Main landing/minting page
│   │   ├── layout.tsx        # App layout
│   │   └── globals.css       # Global styles
│   └── package.json
├── backend/
│   ├── models/
│   │   └── NftUser.js        # NFT user schema
│   ├── index.js              # Main server file
│   ├── mint.js               # NFT minting logic
│   ├── .env                  # Environment variables
│   └── package.json
└── README.md
```

## ⚡ Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd nft-minting-platform

# Install dependencies
npm install
cd frontend && npm install
cd ../backend && npm install

# Set up environment variables
cp backend/.env.example backend/.env
# Edit backend/.env with your MongoDB URI and other configs

# Start the application
npm run dev  # Starts both frontend and backend
```

**Access the app:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- API Health: http://localhost:5000/health

## 🔧 Development Setup

### Prerequisites
- Node.js 18+
- MongoDB Atlas account
- Git
- Ethereum wallet (for production)
- Infura/Alchemy account (for blockchain)

### Detailed Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nft-minting-platform
   ```

2. **Install dependencies**
   ```bash
   # Install frontend dependencies
   cd frontend
   npm install

   # Install backend dependencies
   cd ../backend
   npm install
   ```

3. **Configure environment variables**

   Update `backend/.env`:
   ```env
   MONGO_URI=your_mongodb_connection_string
   PORT=5000

   # Blockchain Configuration (for production)
   RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_KEY
   CONTRACT_ADDRESS=your_nft_contract_address
   PRIVATE_KEY=your_wallet_private_key
   GAS_LIMIT=300000
   GAS_PRICE=***********
   ```

4. **Start the application**
   ```bash
   # Terminal 1: Start backend
   cd backend
   npm start

   # Terminal 2: Start frontend
   cd frontend
   npm run dev
   ```

5. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## 🎯 API Endpoints

### Email Registration
- **POST** `/register-email`
  - Body: `{ "email": "<EMAIL>" }`
  - Response: Success/error message

### NFT Minting
- **POST** `/mint-nft`
  - Body: `{ "email": "<EMAIL>", "wallet": "0x..." }`
  - Response: Minting result with tokenId and metadata

### NFT Information
- **GET** `/nft-info?email=<EMAIL>`
- **GET** `/nft-info?wallet=0x...`
  - Response: NFT details and metadata

### Statistics
- **GET** `/mint-stats`
  - Response: Minting statistics

### Health Check
- **GET** `/health`
  - Response: Server status

## 🎨 Frontend Features

### Two-Mode Interface
1. **Registration Mode**: Email collection with waitlist signup
2. **Minting Mode**: Full NFT minting with wallet input

### UI Components
- **Responsive design** with gradient background
- **Form validation** with real-time feedback
- **Loading states** during API calls
- **Success/error messaging** with color coding
- **NFT result display** with metadata

### User Experience
- **Smooth transitions** between modes
- **Input validation** for email and wallet formats
- **Disabled states** during processing
- **Clear error messages** for better UX

## 🔒 Security Features

- **Input validation** on both frontend and backend
- **Duplicate prevention** for email and wallet addresses
- **Environment variable protection** for sensitive data
- **Error handling** with appropriate HTTP status codes
- **Demo mode** for safe testing

## 🧪 Testing

The platform includes comprehensive validation:
- ✅ Valid email and wallet minting
- ✅ Duplicate email prevention
- ✅ Duplicate wallet prevention
- ✅ Invalid wallet format rejection
- ✅ NFT information retrieval
- ✅ Minting statistics tracking

## 🚀 Production Deployment

For production deployment:

1. **Update environment variables** with real blockchain credentials
2. **Deploy smart contract** to mainnet/testnet
3. **Configure proper RPC endpoint** (Infura, Alchemy, etc.)
4. **Set up monitoring** for minting transactions
5. **Implement proper error handling** for blockchain failures

## 🔗 Smart Contract Integration

### Deployed Contract Integration
The platform is designed to work with **already deployed** smart contracts on Polygon:

#### ✅ **What's Already Done (Contract Deployed):**
- ✅ No need to write Solidity code
- ✅ No need to compile with Hardhat/Foundry
- ✅ No need to deploy to Polygon
- ✅ No need to manage contract state/permissions

**This saves 40-50% of development effort!**

#### 🛠️ **What's Still Needed (Integration Only):**

**1. Get Contract Details:**
```env
CONTRACT_ADDRESS=0xYourDeployedContractAddress
POLYGON_RPC=https://polygon-mainnet.infura.io/v3/YOUR_KEY
PRIVATE_KEY=0xYourWalletPrivateKey
```

**2. Provide Contract ABI:**
Save your contract ABI as `backend/abi.json`

**3. Backend Integration (Already Built):**
```javascript
// Automatic contract method detection
if (contract.mintTo) {
  await contract.mintTo(wallet);
} else if (contract.mint) {
  await contract.mint(wallet, 1);
} else if (contract.safeMint) {
  await contract.safeMint(wallet, 1);
}
```

### Supported Contract Methods
The platform automatically detects and uses:
- `mintTo(address to)`
- `mint(address to)`
- `mint(address to, uint256 quantity)`
- `safeMint(address to, uint256 quantity)`

### Quick Setup Process
1. **Update `.env`** with your contract details
2. **Add `abi.json`** with your contract ABI
3. **Restart server** - automatically switches to real minting
4. **Test endpoint**: `GET /validate-contract`

## 🌐 Environment Configuration

### Required Environment Variables

**Backend (.env):**
```env
# Database
MONGO_URI=mongodb+srv://...

# Blockchain
RPC_URL=https://sepolia.infura.io/v3/YOUR_KEY
CONTRACT_ADDRESS=0x...
CONTRACT_OWNER_PRIVATE_KEY=0x...

# Server
PORT=5000
NODE_ENV=development
```

### Network Configuration
- **Development**: Local Hardhat network
- **Testing**: Sepolia testnet
- **Production**: Ethereum mainnet

## 🧪 Testing

### API Testing
```bash
# Test email registration
curl -X POST http://localhost:5000/register-email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# Test NFT minting
curl -X POST http://localhost:5000/mint-nft \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","wallet":"0x..."}'
```

### Smart Contract Testing
```bash
cd backend
npm run test:contracts
```

## 🚀 Deployment

### Frontend (Vercel)
```bash
npm run build
# Deploy to Vercel
```

### Backend (Railway/Heroku)
```bash
# Set environment variables
# Deploy backend API
```

### Smart Contracts
```bash
# Deploy to mainnet
npm run deploy --network mainnet
```

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenZeppelin for smart contract standards
- Hardhat for development framework
- Next.js and React teams
- MongoDB for database solutions
- Ethereum community for blockchain infrastructure
