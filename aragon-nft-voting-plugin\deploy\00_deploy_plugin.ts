import {HardhatRuntimeEnvironment} from 'hardhat/types';
import {DeployFunction} from 'hardhat-deploy/types';
import {
  PLUGIN_CONTRACT_NAME,
  PLUGIN_SETUP_CONTRACT_NAME,
  VERSION,
} from '../plugin-settings';

const func: DeployFunction = async function (hre: HardhatRuntimeEnvironment) {
  const {deployments, getNamedAccounts} = hre;
  const {deploy} = deployments;
  const {deployer} = await getNamedAccounts();

  console.log(`Deploying ${PLUGIN_CONTRACT_NAME} and ${PLUGIN_SETUP_CONTRACT_NAME}...`);

  // Deploy the plugin implementation
  const pluginImplementation = await deploy(PLUGIN_CONTRACT_NAME, {
    from: deployer,
    args: [],
    log: true,
    autoMine: true,
  });

  console.log(`${PLUGIN_CONTRACT_NAME} deployed at: ${pluginImplementation.address}`);

  // Deploy the plugin setup
  const pluginSetup = await deploy(PLUGIN_SETUP_CONTRACT_NAME, {
    from: deployer,
    args: [],
    log: true,
    autoMine: true,
  });

  console.log(`${PLUGIN_SETUP_CONTRACT_NAME} deployed at: ${pluginSetup.address}`);

  console.log(`Plugin version: ${VERSION.release}.${VERSION.build}`);
};

func.tags = ['Plugin', 'PluginSetup'];
func.dependencies = [];

export default func;
