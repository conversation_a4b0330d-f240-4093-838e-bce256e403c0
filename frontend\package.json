{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "ethers": "^5.7.2", "lucide-react": "^0.511.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "tailwind-variants": "^1.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/vite": "^4.1.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^9", "eslint-config-next": "15.3.3", "nodemon": "^3.1.10", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}