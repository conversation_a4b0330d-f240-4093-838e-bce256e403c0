# 🔗 Deployed Smart Contract Integration Guide

## 📋 Required Information

To integrate with your deployed smart contract, please provide:

### 1. ✅ Contract Address
```
CONTRACT_ADDRESS=0xYourDeployedContractAddress
```

### 2. ✅ Contract ABI
Save the ABI JSON file as `backend/abi.json` or provide the ABI array.

### 3. ✅ Polygon RPC Endpoint
```
# Polygon Mainnet
POLYGON_RPC=https://polygon-mainnet.infura.io/v3/YOUR_INFURA_KEY
# OR
POLYGON_RPC=https://polygon-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY

# Polygon Mumbai Testnet (for testing)
POLYGON_TESTNET_RPC=https://polygon-mumbai.infura.io/v3/YOUR_INFURA_KEY
```

### 4. ✅ Backend Wallet Private Key
```
# Wallet that has permission to mint (if backend-controlled minting)
PRIVATE_KEY=0xYourWalletPrivateKeyWithMintPermission
```

## 🛠️ Contract Method Requirements

Your deployed contract should have one of these minting methods:

### Option A: Simple Mint
```solidity
function mint(address to) external returns (uint256 tokenId)
function mintTo(address to) external returns (uint256 tokenId)
```

### Option B: Quantity Mint
```solidity
function mint(address to, uint256 quantity) external returns (uint256 firstTokenId)
function safeMint(address to, uint256 quantity) external returns (uint256 firstTokenId)
```

### Option C: Custom Mint
```solidity
function yourCustomMintFunction(address to) external returns (uint256 tokenId)
```

## 📝 Setup Steps

1. **Update Environment Variables**
   ```bash
   # Copy your values to backend/.env
   CONTRACT_ADDRESS=0xYourActualContractAddress
   POLYGON_RPC=https://polygon-mainnet.infura.io/v3/YOUR_KEY
   PRIVATE_KEY=0xYourActualPrivateKey
   ```

2. **Provide Contract ABI**
   - Save your contract ABI as `backend/abi.json`
   - OR update the ABI array in `backend/mint.js`

3. **Test Connection**
   ```bash
   # Start the server and check contract info
   curl http://localhost:5000/contract-info
   ```

4. **Test Minting**
   ```bash
   # Test with a real wallet address
   curl -X POST http://localhost:5000/mint-nft \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","wallet":"0xYourTestWallet"}'
   ```

## 🔧 Current Integration Status

✅ **Backend API Ready** - `/mint-nft` endpoint implemented
✅ **Database Schema Ready** - NftUser model with all fields
✅ **Error Handling Ready** - Comprehensive validation and error responses
✅ **Frontend Ready** - Two-mode interface for registration and minting
✅ **Polygon Integration Ready** - Ethers.js setup for Polygon network

## 🎯 What Happens Next

1. **Provide contract details** → System switches from demo to real minting
2. **Test on testnet** → Verify everything works with Mumbai testnet
3. **Deploy to production** → Switch to Polygon mainnet
4. **Monitor transactions** → Track minting success/failures

## 🚨 Security Notes

- **Private Key Security**: Never commit private keys to git
- **RPC Limits**: Monitor API usage on Infura/Alchemy
- **Gas Management**: Ensure backend wallet has sufficient MATIC
- **Contract Permissions**: Verify backend wallet can call mint functions

## 📞 Ready to Integrate?

Once you provide the contract details, the system will automatically:
- Connect to your deployed contract
- Switch from demo mode to real Polygon minting
- Start minting real NFTs to user wallets
- Track all transactions in the database

**The integration is 90% complete - just need your contract details!** 🚀
