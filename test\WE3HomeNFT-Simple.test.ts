import {expect} from 'chai';
import {ethers} from 'hardhat';
import {SignerWithAddress} from '@nomiclabs/hardhat-ethers/signers';

describe('WE3HomeNFT - Simple Test', function () {
  let nft: any;
  let owner: SignerWithAddress;
  let backend: SignerWithAddress;
  let alice: SignerWithAddress;
  let bob: SignerWithAddress;

  const baseURI = 'https://api.we3home.com/metadata/';

  beforeEach(async function () {
    [owner, backend, alice, bob] = await ethers.getSigners();

    // Deploy WE3HomeNFT
    const WE3HomeNFTFactory = await ethers.getContractFactory('WE3HomeNFT');
    nft = await WE3HomeNFTFactory.deploy(baseURI);
    await nft.deployed();

    // Set backend address
    await nft.setBackendAddress(backend.address);
  });

  describe('Basic Functionality', function () {
    it('should deploy with correct initial values', async function () {
      expect(await nft.name()).to.equal('WE3 Home');
      expect(await nft.symbol()).to.equal('WE3');
      expect(await nft.baseTokenURI()).to.equal(baseURI);
      expect(await nft.maxSupply()).to.equal(5200);
      expect(await nft.maxPerWallet()).to.equal(1);
      expect(await nft.totalSupply()).to.equal(0);
      expect(await nft.backendAddress()).to.equal(backend.address);
      expect(await nft.owner()).to.equal(owner.address);
    });

    it('should allow backend to mint NFTs', async function () {
      await expect(nft.connect(backend).backendMint(alice.address))
        .to.emit(nft, 'Minted')
        .withArgs(alice.address, 1);

      expect(await nft.balanceOf(alice.address)).to.equal(1);
      expect(await nft.ownerOf(1)).to.equal(alice.address);
      expect(await nft.hasMinted(alice.address)).to.be.true;
      expect(await nft.totalSupply()).to.equal(1);
    });

    it('should prevent double minting to same address', async function () {
      await nft.connect(backend).backendMint(alice.address);
      
      await expect(nft.connect(backend).backendMint(alice.address))
        .to.be.revertedWith('User already minted');
    });

    it('should prevent non-backend from minting', async function () {
      await expect(nft.connect(alice).backendMint(alice.address))
        .to.be.revertedWith('Only backend can mint');
    });

    it('should return correct token URI', async function () {
      await nft.connect(backend).backendMint(alice.address);
      const tokenURI = await nft.tokenURI(1);
      expect(tokenURI).to.equal(`${baseURI}1.json`);
    });

    it('should enforce one NFT per wallet (perfect for DAO voting)', async function () {
      // Mint to Alice
      await nft.connect(backend).backendMint(alice.address);
      expect(await nft.balanceOf(alice.address)).to.equal(1);
      
      // Mint to Bob
      await nft.connect(backend).backendMint(bob.address);
      expect(await nft.balanceOf(bob.address)).to.equal(1);
      
      // Total supply should be 2
      expect(await nft.totalSupply()).to.equal(2);
      
      // Each wallet has exactly 1 NFT = 1 vote in DAO
      console.log('✅ Perfect for DAO: Each wallet = 1 NFT = 1 vote');
    });
  });

  describe('Admin Functions', function () {
    it('should allow owner to update base URI', async function () {
      const newBaseURI = 'https://new-api.we3home.com/metadata/';
      
      await expect(nft.setBaseURI(newBaseURI))
        .to.emit(nft, 'BaseURIUpdated')
        .withArgs(baseURI, newBaseURI);

      expect(await nft.baseTokenURI()).to.equal(newBaseURI);
    });

    it('should allow owner to update backend address', async function () {
      await expect(nft.setBackendAddress(alice.address))
        .to.emit(nft, 'BackendAddressUpdated')
        .withArgs(backend.address, alice.address);

      expect(await nft.backendAddress()).to.equal(alice.address);
    });
  });

  describe('DAO Voting Compatibility', function () {
    beforeEach(async function () {
      // Mint NFTs to simulate DAO members
      await nft.connect(backend).backendMint(alice.address);   // Member 1
      await nft.connect(backend).backendMint(bob.address);     // Member 2
    });

    it('should be compatible with ERC721 voting requirements', async function () {
      // Test ERC721 interface
      expect(await nft.supportsInterface('0x80ac58cd')).to.be.true; // ERC721

      // Test voting power calculation (1 NFT per wallet = 1 vote per member)
      expect(await nft.balanceOf(alice.address)).to.equal(1);
      expect(await nft.balanceOf(bob.address)).to.equal(1);

      // Test total supply for threshold calculations
      expect(await nft.totalSupply()).to.equal(2);

      // Test token ownership verification
      expect(await nft.ownerOf(1)).to.equal(alice.address);
      expect(await nft.ownerOf(2)).to.equal(bob.address);

      console.log('✅ WE3HomeNFT is perfectly compatible with voting plugin!');
    });

    it('should provide all functions needed by voting plugin', async function () {
      // These are the functions the voting plugin will use
      expect(typeof nft.balanceOf).to.equal('function');
      expect(typeof nft.ownerOf).to.equal('function');
      expect(typeof nft.totalSupply).to.equal('function');
      expect(typeof nft.supportsInterface).to.equal('function');

      // Test they work correctly
      const balance = await nft.balanceOf(alice.address);
      const owner = await nft.ownerOf(1);
      const supply = await nft.totalSupply();
      
      expect(balance).to.equal(1);
      expect(owner).to.equal(alice.address);
      expect(supply).to.equal(2);

      console.log('✅ All voting plugin functions work correctly!');
    });
  });
});
