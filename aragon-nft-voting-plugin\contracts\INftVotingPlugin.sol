// SPDX-License-Identifier: AGPL-3.0-or-later
pragma solidity 0.8.17;

import {<PERSON><PERSON><PERSON>} from "@aragon/osx/core/dao/IDAO.sol";
import {IERC721} from "@openzeppelin/contracts/token/ERC721/IERC721.sol";

/// @title INftVotingPlugin
/// <AUTHOR>
/// @notice Interface for the NFT Voting Plugin
interface INftVotingPlugin {
    /// @notice The different voting modes available
    enum VotingMode {
        Standard,
        EarlyExecution,
        VoteReplacement
    }

    /// @notice The different vote options available
    enum VoteOption {
        None,
        Abstain,
        Yes,
        No
    }

    /// @notice A container for the voting settings
    struct VotingSettings {
        VotingMode votingMode;
        uint32 supportThreshold;
        uint32 minParticipation;
        uint64 minDuration;
        uint256 minProposerVotingPower;
    }

    /// @notice A container for proposal parameters
    struct ProposalParameters {
        VotingMode votingMode;
        uint32 supportThreshold;
        uint32 minParticipation;
        uint64 startDate;
        uint64 endDate;
        uint64 snapshotBlock;
        uint256 minProposerVotingPower;
    }

    /// @notice A container for the proposal vote tally
    struct Tally {
        uint256 abstain;
        uint256 yes;
        uint256 no;
    }

    /// @notice Emitted when a proposal is created
    event ProposalCreated(
        uint256 indexed proposalId,
        address indexed creator,
        uint64 startDate,
        uint64 endDate,
        bytes metadata,
        IDAO.Action[] actions,
        uint256 allowFailureMap
    );

    /// @notice Emitted when a vote is cast
    event VoteCast(
        uint256 indexed proposalId,
        address indexed voter,
        uint256 indexed tokenId,
        VoteOption voteOption
    );

    /// @notice Emitted when a proposal is executed
    event ProposalExecuted(uint256 indexed proposalId);

    /// @notice Emitted when voting settings are updated
    event VotingSettingsUpdated(
        VotingMode votingMode,
        uint32 supportThreshold,
        uint32 minParticipation,
        uint64 minDuration,
        uint256 minProposerVotingPower
    );

    /// @notice Updates the voting settings
    /// @param _votingSettings The new voting settings
    function updateVotingSettings(VotingSettings calldata _votingSettings) external;

    /// @notice Creates a new proposal
    /// @param _metadata The metadata of the proposal
    /// @param _actions The actions to be executed if the proposal passes
    /// @param _allowFailureMap The failure map of the proposal
    /// @param _startDate The start date of the proposal vote
    /// @param _endDate The end date of the proposal vote
    /// @return proposalId The ID of the newly created proposal
    function createProposal(
        bytes calldata _metadata,
        IDAO.Action[] calldata _actions,
        uint256 _allowFailureMap,
        uint64 _startDate,
        uint64 _endDate
    ) external returns (uint256 proposalId);

    /// @notice Votes on a proposal using an NFT token
    /// @param _proposalId The ID of the proposal
    /// @param _tokenId The ID of the NFT token to use for voting
    /// @param _voteOption The vote option (Yes, No, Abstain)
    function vote(uint256 _proposalId, uint256 _tokenId, VoteOption _voteOption) external;

    /// @notice Executes a proposal if it has passed
    /// @param _proposalId The ID of the proposal to execute
    function execute(uint256 _proposalId) external;

    /// @notice Returns whether a proposal can be executed
    /// @param _proposalId The ID of the proposal
    /// @return True if the proposal can be executed, false otherwise
    function canExecute(uint256 _proposalId) external view returns (bool);

    /// @notice Returns whether a proposal has passed
    /// @param _proposalId The ID of the proposal
    /// @return True if the proposal has passed, false otherwise
    function hasProposalPassed(uint256 _proposalId) external view returns (bool);

    /// @notice Returns the proposal information
    /// @param _proposalId The ID of the proposal
    /// @return executed Whether the proposal is executed
    /// @return parameters The proposal parameters
    /// @return tally The vote tally
    /// @return actions The actions to be executed
    /// @return allowFailureMap The failure map
    function getProposal(uint256 _proposalId)
        external
        view
        returns (
            bool executed,
            ProposalParameters memory parameters,
            Tally memory tally,
            IDAO.Action[] memory actions,
            uint256 allowFailureMap
        );

    /// @notice Returns whether a token has voted on a proposal
    /// @param _proposalId The ID of the proposal
    /// @param _tokenId The ID of the token
    /// @return True if the token has voted, false otherwise
    function hasTokenVoted(uint256 _proposalId, uint256 _tokenId) external view returns (bool);

    /// @notice Returns the total voting power at a specific block
    /// @param _blockNumber The block number
    /// @return The total voting power
    function totalVotingPower(uint256 _blockNumber) external view returns (uint256);

    /// @notice Returns the voting power of an account at a specific block
    /// @param _account The account address
    /// @param _blockNumber The block number
    /// @return The voting power
    function getVotingPower(address _account, uint256 _blockNumber) external view returns (uint256);

    /// @notice Returns the NFT token used for voting
    /// @return The NFT token contract
    function nftToken() external view returns (IERC721);

    /// @notice Returns the current voting settings
    /// @return The voting settings
    function votingSettings() external view returns (VotingSettings memory);

    /// @notice Returns the proposal counter
    /// @return The number of proposals created
    function proposalCounter() external view returns (uint256);
}
