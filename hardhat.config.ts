import '@nomicfoundation/hardhat-chai-matchers';
import '@nomicfoundation/hardhat-network-helpers';
import '@nomicfoundation/hardhat-verify';
import '@nomiclabs/hardhat-ethers';
import '@nomiclabs/hardhat-etherscan';
import '@typechain/hardhat';
import 'hardhat-gas-reporter';
import 'solidity-coverage';

import {HardhatUserConfig} from 'hardhat/config';
import {NetworkUserConfig} from 'hardhat/types';

import * as dotenv from 'dotenv';

dotenv.config();

// Default test mnemonic - DO NOT use in production
const DEFAULT_MNEMONIC = 'test test test test test test test test test test test junk';

// Environment variables with defaults
const mnemonic: string = process.env.MNEMONIC || DEFAULT_MNEMONIC;
const infuraApiKey: string = process.env.INFURA_API_KEY || '';
const alchemyApiKey: string = process.env.ALCHEMY_API_KEY || '';

function getChainConfig(chain: keyof typeof chainIds): NetworkUserConfig {
  let jsonRpcUrl: string;
  
  switch (chain) {
    case 'mainnet':
      jsonRpcUrl = alchemyApiKey 
        ? `https://eth-mainnet.alchemyapi.io/v2/${alchemyApiKey}`
        : `https://mainnet.infura.io/v3/${infuraApiKey}`;
      break;
    case 'sepolia':
      jsonRpcUrl = alchemyApiKey
        ? `https://eth-sepolia.g.alchemy.com/v2/${alchemyApiKey}`
        : `https://sepolia.infura.io/v3/${infuraApiKey}`;
      break;
    case 'polygon':
      jsonRpcUrl = alchemyApiKey
        ? `https://polygon-mainnet.g.alchemy.com/v2/${alchemyApiKey}`
        : `https://polygon-mainnet.infura.io/v3/${infuraApiKey}`;
      break;
    case 'polygonMumbai':
      jsonRpcUrl = alchemyApiKey
        ? `https://polygon-mumbai.g.alchemy.com/v2/${alchemyApiKey}`
        : `https://polygon-mumbai.infura.io/v3/${infuraApiKey}`;
      break;
    case 'arbitrum':
      jsonRpcUrl = alchemyApiKey
        ? `https://arb-mainnet.g.alchemy.com/v2/${alchemyApiKey}`
        : `https://arbitrum-mainnet.infura.io/v3/${infuraApiKey}`;
      break;
    case 'arbitrumSepolia':
      jsonRpcUrl = alchemyApiKey
        ? `https://arb-sepolia.g.alchemy.com/v2/${alchemyApiKey}`
        : `https://arbitrum-sepolia.infura.io/v3/${infuraApiKey}`;
      break;
    default:
      jsonRpcUrl = `https://${chain}.infura.io/v3/${infuraApiKey}`;
  }
  
  return {
    accounts: {
      count: 10,
      mnemonic,
      path: "m/44'/60'/0'/0",
    },
    chainId: chainIds[chain],
    url: jsonRpcUrl,
    gasPrice: 'auto',
    gas: 'auto',
  };
}

const chainIds = {
  hardhat: 31337,
  mainnet: 1,
  sepolia: ********,
  polygon: 137,
  polygonMumbai: 80001,
  arbitrum: 42161,
  arbitrumSepolia: 421614,
  base: 8453,
  baseSepolia: 84532,
  optimism: 10,
  optimismSepolia: ********,
};

const config: HardhatUserConfig = {
  defaultNetwork: 'hardhat',
  etherscan: {
    apiKey: {
      mainnet: process.env.ETHERSCAN_API_KEY || '',
      sepolia: process.env.ETHERSCAN_API_KEY || '',
      polygon: process.env.POLYGONSCAN_API_KEY || '',
      polygonMumbai: process.env.POLYGONSCAN_API_KEY || '',
      arbitrumOne: process.env.ARBISCAN_API_KEY || '',
      arbitrumSepolia: process.env.ARBISCAN_API_KEY || '',
      base: process.env.BASESCAN_API_KEY || '',
      baseSepolia: process.env.BASESCAN_API_KEY || '',
      optimisticEthereum: process.env.OPTIMISM_API_KEY || '',
      optimisticSepolia: process.env.OPTIMISM_API_KEY || '',
    },
  },
  gasReporter: {
    currency: 'USD',
    enabled: process.env.REPORT_GAS === 'true',
    excludeContracts: [],
    src: './contracts',
    coinmarketcap: process.env.COINMARKETCAP_API_KEY,
  },
  networks: {
    hardhat: {
      accounts: {
        mnemonic,
        count: 20,
      },
      chainId: chainIds.hardhat,
      // Uncomment for mainnet forking
      // forking: {
      //   url: `https://mainnet.infura.io/v3/${infuraApiKey}`,
      //   blockNumber: ********,
      // },
    },
    localhost: {
      url: 'http://127.0.0.1:8545',
      accounts: {
        mnemonic,
      },
    },
    // Mainnets
    mainnet: getChainConfig('mainnet'),
    polygon: getChainConfig('polygon'),
    arbitrum: getChainConfig('arbitrum'),
    base: getChainConfig('base'),
    optimism: getChainConfig('optimism'),
    
    // Testnets
    sepolia: getChainConfig('sepolia'),
    polygonMumbai: getChainConfig('polygonMumbai'),
    arbitrumSepolia: getChainConfig('arbitrumSepolia'),
    baseSepolia: getChainConfig('baseSepolia'),
    optimismSepolia: getChainConfig('optimismSepolia'),
  },
  paths: {
    artifacts: './artifacts',
    cache: './cache',
    sources: './contracts',
    tests: './test',
  },
  solidity: {
    version: '0.8.28', // Match your WE3HomeNFT contract
    settings: {
      metadata: {
        bytecodeHash: 'none',
      },
      optimizer: {
        enabled: true,
        runs: 800,
      },
      viaIR: false,
    },
  },
  typechain: {
    outDir: 'typechain',
    target: 'ethers-v5',
    alwaysGenerateOverloads: false,
    externalArtifacts: ['externalArtifacts/*.json'],
  },
  mocha: {
    timeout: 60000,
  },
};

export default config;
