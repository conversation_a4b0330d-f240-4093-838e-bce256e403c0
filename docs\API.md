# API Documentation

## Base URL
```
Development: http://localhost:5000
Production: https://your-api-domain.com
```

## Authentication
Currently, the API does not require authentication for public endpoints. Future versions may include JWT-based authentication.

## Endpoints

### Health Check
**GET** `/health`

Check if the server is running.

**Response:**
```json
{
  "status": "OK",
  "message": "Server is running"
}
```

### Email Registration
**POST** `/register-email`

Register an email for the waitlist.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Responses:**
- **200 OK**: Email registered successfully
- **400 Bad Request**: Invalid email or already registered
- **500 Internal Server Error**: Server error

### NFT Minting
**POST** `/mint-nft`

Mint an NFT to a specified wallet address.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "wallet": "******************************************"
}
```

**Success Response (200):**
```json
{
  "message": "NFT minted successfully!",
  "tokenId": 123,
  "transactionHash": "0x...",
  "metadata": {
    "name": "NFT Pass #123",
    "description": "Exclusive NFT Pass #123 for early supporters",
    "image": "https://api.example.com/nft/123/image.png",
    "attributes": [...]
  },
  "mintedAt": "2024-01-01T00:00:00.000Z"
}
```

**Error Responses:**
- **400 Bad Request**: Invalid input or already minted
- **500 Internal Server Error**: Minting failed

### Get NFT Information
**GET** `/nft-info`

Retrieve NFT information by email or wallet address.

**Query Parameters:**
- `email`: Email address
- `wallet`: Wallet address

**Example:**
```
GET /nft-info?email=<EMAIL>
GET /nft-info?wallet=******************************************
```

**Response:**
```json
{
  "email": "<EMAIL>",
  "wallet": "******************************************",
  "tokenId": 123,
  "transactionHash": "0x...",
  "status": "minted",
  "metadata": {...},
  "mintedAt": "2024-01-01T00:00:00.000Z"
}
```

### Minting Statistics
**GET** `/mint-stats`

Get overall minting statistics.

**Response:**
```json
{
  "totalMinted": 150,
  "totalFailed": 5,
  "totalPending": 2,
  "totalAttempts": 157
}
```

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:

```json
{
  "error": "Error description",
  "details": "Additional error details (optional)"
}
```

## Rate Limiting

- Default: 100 requests per 15 minutes per IP
- Minting endpoints: 5 requests per hour per IP

## CORS

CORS is enabled for all origins in development. In production, configure specific allowed origins.
