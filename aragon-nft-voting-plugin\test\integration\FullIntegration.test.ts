import {expect} from 'chai';
import {ethers} from 'hardhat';
import {SignerWithAddress} from '@nomiclabs/hardhat-ethers/signers';
import {
  NftVotingPlugin,
  NftVotingPluginSetup,
  MockNFT,
  MockDAO,
  RoleProposalHelper,
} from '../../typechain';

describe('Full Integration Test', function () {
  let plugin: NftVotingPlugin;
  let pluginSetup: NftVotingPluginSetup;
  let mockNFT: MockNFT;
  let dao: MockDAO;
  let roleHelper: RoleProposalHelper;
  
  let deployer: SignerWithAddress;
  let alice: SignerWithAddress;
  let bob: Signer<PERSON>ithAddress;
  let charlie: SignerWithAddress;
  let david: Signer<PERSON>ithAddress;
  let manager: Signer<PERSON>ithAddress;

  const votingSettings = {
    votingMode: 0, // Standard
    supportThreshold: 500000, // 50%
    minParticipation: 250000, // 25%
    minDuration: 3600, // 1 hour for testing
    minProposerVotingPower: 1, // 1 NFT
  };

  before(async function () {
    [deployer, alice, bob, charlie, david, manager] = await ethers.getSigners();
    
    console.log('🏗️  Setting up full integration test environment...');
    
    // Deploy all contracts
    await deployAllContracts();
    await setupInitialState();
    
    console.log('✅ Integration test environment ready');
  });

  async function deployAllContracts() {
    // Deploy mock NFT contract
    const MockNFTFactory = await ethers.getContractFactory('MockNFT');
    mockNFT = await MockNFTFactory.deploy(
      'DAO Governance NFT',
      'DGNFT',
      'https://dao.example.com/metadata/'
    );
    await mockNFT.deployed();

    // Deploy mock DAO
    const MockDAOFactory = await ethers.getContractFactory('MockDAO');
    dao = await MockDAOFactory.deploy();
    await dao.deployed();

    // Deploy role helper
    const RoleHelperFactory = await ethers.getContractFactory('RoleProposalHelper');
    roleHelper = await RoleHelperFactory.deploy();
    await roleHelper.deployed();

    // Deploy plugin setup
    const PluginSetupFactory = await ethers.getContractFactory('NftVotingPluginSetup');
    pluginSetup = await PluginSetupFactory.deploy();
    await pluginSetup.deployed();

    // Prepare installation
    const installationParams = await pluginSetup.encodeInstallationParams(
      mockNFT.address,
      votingSettings
    );

    const installationResult = await pluginSetup.prepareInstallation(
      dao.address,
      installationParams
    );

    plugin = await ethers.getContractAt('NftVotingPlugin', installationResult.plugin);

    console.log('📦 All contracts deployed successfully');
  }

  async function setupInitialState() {
    // Mint NFTs to test users
    await mockNFT.mint(alice.address);     // Token 1
    await mockNFT.mint(bob.address);       // Token 2
    await mockNFT.mint(charlie.address);   // Token 3
    await mockNFT.mint(david.address);     // Token 4
    await mockNFT.mintBatch(alice.address, 2); // Tokens 5, 6
    await mockNFT.mint(manager.address);   // Token 7

    console.log('🎨 NFTs minted to test accounts');
  }

  describe('Complete Workflow: Manager Appointment', function () {
    let proposalId: number;

    it('should create a manager appointment proposal', async function () {
      // Create manager appointment actions using role helper
      const managerActions = await roleHelper.createManagerAppointmentActions(
        dao.address,
        manager.address
      );

      const metadata = await roleHelper.createRoleProposalMetadata(
        0, // ProposalType.ManagerAppointment
        [manager.address],
        'Proposal to appoint a new DAO Manager with oversight responsibilities'
      );

      const startDate = Math.floor(Date.now() / 1000) + 60;
      const endDate = startDate + 3600;

      const tx = await plugin.connect(alice).createProposal(
        metadata,
        managerActions,
        0, // allowFailureMap
        startDate,
        endDate
      );

      const receipt = await tx.wait();
      proposalId = 1;

      expect(receipt.events?.find(e => e.event === 'ProposalCreated')).to.not.be.undefined;
      expect(await plugin.proposalCounter()).to.equal(1);

      console.log('✅ Manager appointment proposal created');
    });

    it('should allow NFT holders to vote on the proposal', async function () {
      // Fast forward to voting start
      await ethers.provider.send('evm_increaseTime', [65]);
      await ethers.provider.send('evm_mine', []);

      // Multiple users vote
      await plugin.connect(alice).vote(proposalId, 1, 2);   // Yes
      await plugin.connect(alice).vote(proposalId, 5, 2);   // Yes
      await plugin.connect(bob).vote(proposalId, 2, 2);     // Yes
      await plugin.connect(charlie).vote(proposalId, 3, 3); // No
      await plugin.connect(david).vote(proposalId, 4, 1);   // Abstain

      const proposal = await plugin.getProposal(proposalId);
      expect(proposal.tally.yes).to.equal(3);
      expect(proposal.tally.no).to.equal(1);
      expect(proposal.tally.abstain).to.equal(1);

      console.log('✅ Voting completed - 3 Yes, 1 No, 1 Abstain');
    });

    it('should execute the proposal and grant manager role', async function () {
      // Fast forward to end of voting period
      await ethers.provider.send('evm_increaseTime', [3601]);
      await ethers.provider.send('evm_mine', []);

      expect(await plugin.canExecute(proposalId)).to.be.true;
      expect(await plugin.hasProposalPassed(proposalId)).to.be.true;

      // Execute the proposal
      const tx = await plugin.execute(proposalId);
      const receipt = await tx.wait();

      expect(receipt.events?.find(e => e.event === 'ProposalExecuted')).to.not.be.undefined;

      // Verify the proposal is marked as executed
      const proposal = await plugin.getProposal(proposalId);
      expect(proposal.executed).to.be.true;

      console.log('✅ Proposal executed successfully');
    });

    it('should verify manager role was granted through DAO', async function () {
      // Check if the DAO received the grant action
      const executedActionsCount = await dao.getExecutedActionsCount();
      expect(executedActionsCount).to.equal(1);

      const executedAction = await dao.getExecutedAction(0);
      expect(executedAction.to).to.equal(dao.address);

      // Decode the action data to verify it's a grant call
      const expectedData = dao.interface.encodeFunctionData('grant', [
        dao.address,
        manager.address,
        await roleHelper.MANAGER_ROLE(),
      ]);
      expect(executedAction.data).to.equal(expectedData);

      console.log('✅ Manager role grant action executed through DAO');
    });
  });

  describe('Complete Workflow: Council Member Appointment', function () {
    let councilProposalId: number;

    it('should create a council member appointment proposal', async function () {
      const councilMembers = [bob.address, charlie.address];
      const councilActions = await roleHelper.createCouncilAppointmentActions(
        dao.address,
        councilMembers
      );

      const metadata = await roleHelper.createRoleProposalMetadata(
        1, // ProposalType.CouncilAppointment
        councilMembers,
        'Proposal to appoint new Council Members for governance participation'
      );

      const startDate = Math.floor(Date.now() / 1000) + 60;
      const endDate = startDate + 3600;

      await plugin.connect(alice).createProposal(
        metadata,
        councilActions,
        0,
        startDate,
        endDate
      );

      councilProposalId = 2;
      expect(await plugin.proposalCounter()).to.equal(2);

      console.log('✅ Council appointment proposal created');
    });

    it('should handle voting with different participation levels', async function () {
      // Fast forward to voting start
      await ethers.provider.send('evm_increaseTime', [65]);
      await ethers.provider.send('evm_mine', []);

      // Only some users vote (testing minimum participation)
      await plugin.connect(alice).vote(councilProposalId, 6, 2); // Yes
      await plugin.connect(manager).vote(councilProposalId, 7, 2); // Yes

      const proposal = await plugin.getProposal(councilProposalId);
      expect(proposal.tally.yes).to.equal(2);
      expect(proposal.tally.no).to.equal(0);
      expect(proposal.tally.abstain).to.equal(0);

      // Check if proposal meets thresholds
      const totalVotingPower = await plugin.totalVotingPower(0);
      const totalVotes = proposal.tally.yes.add(proposal.tally.no).add(proposal.tally.abstain);
      const participationRate = totalVotes.mul(1000000).div(totalVotingPower);

      console.log(`📊 Participation: ${participationRate.toNumber() / 10000}%`);
      console.log(`📊 Required: ${votingSettings.minParticipation / 10000}%`);
    });

    it('should execute council appointment if thresholds are met', async function () {
      // Fast forward to end of voting period
      await ethers.provider.send('evm_increaseTime', [3601]);
      await ethers.provider.send('evm_mine', []);

      const canExecute = await plugin.canExecute(councilProposalId);
      const hasPassed = await plugin.hasProposalPassed(councilProposalId);

      if (canExecute && hasPassed) {
        await plugin.execute(councilProposalId);
        
        // Verify multiple actions were executed
        const executedActionsCount = await dao.getExecutedActionsCount();
        expect(executedActionsCount).to.be.greaterThan(1);

        console.log('✅ Council appointment executed');
      } else {
        console.log('⚠️  Council proposal did not meet execution thresholds');
      }
    });
  });

  describe('Edge Cases and Error Handling', function () {
    it('should prevent double voting with same token', async function () {
      // Create a new proposal
      const metadata = ethers.utils.toUtf8Bytes('Test double voting prevention');
      const startDate = Math.floor(Date.now() / 1000) + 60;
      const endDate = startDate + 3600;

      await plugin.connect(alice).createProposal(
        metadata,
        [],
        0,
        startDate,
        endDate
      );

      const testProposalId = 3;

      // Fast forward to voting start
      await ethers.provider.send('evm_increaseTime', [65]);
      await ethers.provider.send('evm_mine', []);

      // First vote should succeed
      await plugin.connect(alice).vote(testProposalId, 1, 2);

      // Second vote with same token should fail
      await expect(
        plugin.connect(alice).vote(testProposalId, 1, 3)
      ).to.be.revertedWith('TokenAlreadyVoted');

      console.log('✅ Double voting prevention working');
    });

    it('should prevent voting with non-owned NFT', async function () {
      const testProposalId = 3;

      // Alice tries to vote with Bob's token
      await expect(
        plugin.connect(alice).vote(testProposalId, 2, 2)
      ).to.be.revertedWith('VoteCastForbidden');

      console.log('✅ Non-owner voting prevention working');
    });

    it('should handle proposal execution failures gracefully', async function () {
      // Create a proposal with invalid actions
      const invalidActions = [{
        to: ethers.constants.AddressZero,
        value: 0,
        data: '0x',
      }];

      const startDate = Math.floor(Date.now() / 1000) + 60;
      const endDate = startDate + 3600;

      await plugin.connect(alice).createProposal(
        ethers.utils.toUtf8Bytes('Invalid action proposal'),
        invalidActions,
        1, // Allow failure
        startDate,
        endDate
      );

      const invalidProposalId = 4;

      // Vote and execute
      await ethers.provider.send('evm_increaseTime', [65]);
      await ethers.provider.send('evm_mine', []);

      await plugin.connect(alice).vote(invalidProposalId, 5, 2);
      await plugin.connect(bob).vote(invalidProposalId, 2, 2);

      await ethers.provider.send('evm_increaseTime', [3601]);
      await ethers.provider.send('evm_mine', []);

      // Should execute without reverting due to allowFailureMap
      await plugin.execute(invalidProposalId);

      console.log('✅ Graceful failure handling working');
    });
  });

  describe('Plugin State and View Functions', function () {
    it('should return correct plugin state information', async function () {
      const settings = await plugin.votingSettings();
      expect(settings.supportThreshold).to.equal(votingSettings.supportThreshold);
      expect(settings.minParticipation).to.equal(votingSettings.minParticipation);

      const nftToken = await plugin.nftToken();
      expect(nftToken).to.equal(mockNFT.address);

      const proposalCount = await plugin.proposalCounter();
      expect(proposalCount).to.be.greaterThan(0);

      console.log('✅ Plugin state functions working correctly');
    });

    it('should return correct voting power calculations', async function () {
      const aliceVotingPower = await plugin.getVotingPower(alice.address, 0);
      const totalVotingPower = await plugin.totalVotingPower(0);

      expect(aliceVotingPower).to.equal(3); // Alice has 3 NFTs
      expect(totalVotingPower).to.equal(7); // Total 7 NFTs

      console.log('✅ Voting power calculations correct');
    });
  });

  after(async function () {
    console.log('\n📊 Integration Test Summary:');
    console.log('='.repeat(50));
    console.log(`Total Proposals Created: ${await plugin.proposalCounter()}`);
    console.log(`Total DAO Actions Executed: ${await dao.getExecutedActionsCount()}`);
    console.log(`Total NFTs in Circulation: ${await mockNFT.totalSupply()}`);
    console.log('Plugin Functionality: ✅ Fully Working');
    console.log('Role Management: ✅ Working');
    console.log('Vote Counting: ✅ Accurate');
    console.log('Error Handling: ✅ Robust');
    console.log('='.repeat(50));
  });
});
