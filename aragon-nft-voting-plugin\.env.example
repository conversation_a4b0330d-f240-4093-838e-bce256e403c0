# Mnemonic for deploying contracts
MNEMONIC="test test test test test test test test test test test junk"

# API Keys for blockchain providers
INFURA_API_KEY=your_infura_api_key_here
ALCHEMY_API_KEY=your_alchemy_api_key_here

# API Keys for block explorers
ETHERSCAN_API_KEY=your_etherscan_api_key_here
POLYGONSCAN_API_KEY=your_polygonscan_api_key_here
BSCSCAN_API_KEY=your_bscscan_api_key_here
SNOWTRACE_API_KEY=your_snowtrace_api_key_here

# Gas reporting
REPORT_GAS=false

# Plugin settings
PLUGIN_REPO_ENS_NAME=nft-voting-plugin
VERSION=1.1

# NFT Contract Address (for testing)
NFT_CONTRACT_ADDRESS=******************************************

# DAO settings for testing
TEST_DAO_ADDRESS=******************************************
MANAGER_ROLE=0x241ecf16d79d0f8dbfb92cbc07fe17840425976cf0667f022fe9877caa831b08
COUNCIL_ROLE=0x71840dc4906352362b0cdaf79870196c8e42acafade72d5d5a6d59291253ceb1
