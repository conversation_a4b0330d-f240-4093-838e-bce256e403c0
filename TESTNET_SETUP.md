# 🌐 Testnet Setup Guide

This guide will help you deploy and test your WE3HomeNFT contract and Aragon voting plugin on testnet.

## 📋 Prerequisites

1. **Node.js** >= 18.0.0
2. **npm** >= 8.0.0
3. **Git**
4. **Testnet ETH** (for gas fees)

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
# Install all dependencies
npm run install-all

# Or install individually
npm install
cd frontend && npm install
cd ../backend && npm install
cd ../aragon-nft-voting-plugin && npm install
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit the .env file with your configuration
nano .env
```

**Required Environment Variables:**
```bash
# Wallet (use test wallet for testnet!)
MNEMONIC="your twelve word mnemonic here"

# RPC Provider (get free API key)
INFURA_API_KEY=your_infura_api_key
# OR
ALCHEMY_API_KEY=your_alchemy_api_key

# Block Explorer (for contract verification)
ETHERSCAN_API_KEY=your_etherscan_api_key

# NFT Configuration
NFT_BASE_URI=https://api.we3home.com/metadata/
BACKEND_ADDRESS=******************************************

# Deployment Options
DEPLOY_PLUGIN=true
MINT_TEST_NFTS=true
VERIFY_CONTRACTS=true
```

### 3. Get Testnet ETH

**Sepolia Testnet (Recommended):**
- [Sepolia Faucet 1](https://sepoliafaucet.com/)
- [Sepolia Faucet 2](https://www.alchemy.com/faucets/ethereum-sepolia)
- [Chainlink Faucet](https://faucets.chain.link/)

**Other Testnets:**
- [Polygon Mumbai Faucet](https://faucet.polygon.technology/)
- [Arbitrum Sepolia Faucet](https://bridge.arbitrum.io/)

## 🧪 Testing Your Contracts

### 1. Local Testing

```bash
# Test your WE3HomeNFT contract
npm run test:nft

# Test integration with voting plugin
npm run test:integration

# Test the voting plugin separately
npm run test:plugin

# Run all contract tests
npm run test:contracts

# Generate coverage report
npm run coverage
```

### 2. Compile Contracts

```bash
# Compile all contracts
npm run build:contracts

# Generate TypeScript types
npm run typechain
```

## 🌐 Testnet Deployment

### 1. Deploy to Sepolia (Recommended)

```bash
# Deploy everything to Sepolia testnet
npm run deploy:testnet

# Or deploy step by step:

# 1. Deploy NFT contract only
npm run deploy:nft -- --network sepolia

# 2. Deploy voting plugin separately
npm run deploy:plugin -- --network sepolia

# 3. Verify contracts
npm run verify:nft -- --network sepolia
```

### 2. Deploy to Other Testnets

```bash
# Polygon Mumbai
npm run deploy:testnet -- --network polygonMumbai

# Arbitrum Sepolia
npm run deploy:testnet -- --network arbitrumSepolia

# Base Sepolia
npm run deploy:testnet -- --network baseSepolia
```

### 3. Manual Deployment Steps

If you prefer manual control:

```bash
# 1. Deploy WE3HomeNFT
npx hardhat run scripts/deploy-nft.ts --network sepolia

# 2. Set environment variables with deployed addresses
export WE3HOME_NFT_ADDRESS=0x...

# 3. Deploy voting plugin
cd aragon-nft-voting-plugin
export NFT_CONTRACT_ADDRESS=$WE3HOME_NFT_ADDRESS
npm run deploy -- --network sepolia

# 4. Verify contracts
npx hardhat verify --network sepolia $WE3HOME_NFT_ADDRESS "https://api.we3home.com/metadata/"
```

## 🔍 Verification & Testing

### 1. Verify Deployment

After deployment, verify your contracts:

```bash
# Check contract on Etherscan
# Visit: https://sepolia.etherscan.io/address/YOUR_CONTRACT_ADDRESS

# Verify contract source code
npx hardhat verify --network sepolia <CONTRACT_ADDRESS> <CONSTRUCTOR_ARGS>
```

### 2. Test Contract Functions

```bash
# Start Hardhat console
npx hardhat console --network sepolia

# In the console:
const nft = await ethers.getContractAt("WE3HomeNFT", "YOUR_CONTRACT_ADDRESS");
console.log("Name:", await nft.name());
console.log("Symbol:", await nft.symbol());
console.log("Total Supply:", await nft.totalSupply());
console.log("Max Supply:", await nft.maxSupply());
```

### 3. Test Minting

```bash
# Test backend minting (replace with your addresses)
const [deployer] = await ethers.getSigners();
const nft = await ethers.getContractAt("WE3HomeNFT", "YOUR_CONTRACT_ADDRESS");

# Set backend address (if not already set)
await nft.setBackendAddress(deployer.address);

# Mint test NFT
await nft.backendMint("******************************************");
console.log("Total Supply after mint:", await nft.totalSupply());
```

## 🗳️ Testing Voting Plugin

### 1. Deploy Plugin with Your NFT

```bash
cd aragon-nft-voting-plugin

# Set your NFT contract address
export NFT_CONTRACT_ADDRESS=0x... # Your deployed WE3HomeNFT address
export DAO_ADDRESS=0x...          # Your test DAO address (or deploy MockDAO)

# Deploy plugin
npm run deploy -- --network sepolia
```

### 2. Test Voting Functionality

```bash
# Run integration tests with your deployed contracts
export PLUGIN_ADDRESS=0x...      # Your deployed plugin address
export NFT_CONTRACT_ADDRESS=0x... # Your NFT contract address
export DAO_ADDRESS=0x...          # Your DAO address

npm run test:integration
```

### 3. Create Test Proposals

Use the test script to create and vote on proposals:

```bash
# Run the plugin test script
npx hardhat run aragon-nft-voting-plugin/scripts/test-plugin.ts --network sepolia
```

## 📊 Monitoring & Debugging

### 1. Check Transaction Status

```bash
# View transaction on Etherscan
# https://sepolia.etherscan.io/tx/YOUR_TX_HASH

# Check gas usage
npx hardhat run scripts/gas-analysis.ts --network sepolia
```

### 2. Debug Failed Transactions

```bash
# Enable verbose logging
export DEBUG=true
export VERBOSE_LOGGING=true

# Run with detailed output
npm run deploy:testnet -- --network sepolia
```

### 3. Monitor Contract Events

```bash
# Listen to contract events
npx hardhat run scripts/event-listener.ts --network sepolia
```

## 🔧 Troubleshooting

### Common Issues

1. **"Insufficient funds for gas"**
   - Get more testnet ETH from faucets
   - Check your wallet balance

2. **"Nonce too high"**
   - Reset your MetaMask account
   - Or use: `npx hardhat clean`

3. **"Contract verification failed"**
   - Wait a few minutes and try again
   - Check constructor arguments match exactly

4. **"Network not supported"**
   - Check your hardhat.config.ts network configuration
   - Verify RPC URLs are correct

### Getting Help

1. **Check deployment logs** in `./deployments/` folder
2. **Review test results** for any failures
3. **Check contract addresses** in deployment files
4. **Verify environment variables** are set correctly

## 🎯 Next Steps

After successful testnet deployment:

1. **Update Frontend**: Configure your frontend with contract addresses
2. **Update Backend**: Set up backend minting with deployed contract
3. **Test User Flow**: Test complete user registration and minting flow
4. **Monitor Performance**: Set up monitoring and alerts
5. **Prepare Mainnet**: Plan mainnet deployment strategy

## 📚 Additional Resources

- [Hardhat Documentation](https://hardhat.org/docs)
- [Aragon OSx Documentation](https://docs.aragon.org/osx/)
- [OpenZeppelin Contracts](https://docs.openzeppelin.com/contracts/)
- [Etherscan API](https://docs.etherscan.io/)

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review deployment logs in `./deployments/`
3. Test contracts individually before integration
4. Verify all environment variables are set correctly
