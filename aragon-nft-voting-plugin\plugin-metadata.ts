import {PluginRepo} from '@aragon/osx-ethers';

export const PLUGIN_METADATA = {
  name: 'NFT Voting Plugin',
  description: 'A plugin that allows NFT holders to vote on DAO proposals using ERC-721 tokens. Each NFT represents 1 vote and can only be used once per proposal.',
  avatar: 'https://github.com/aragon/nft-voting-plugin/raw/main/assets/icon.svg',
  links: {
    homepage: 'https://github.com/aragon/nft-voting-plugin',
    repository: 'https://github.com/aragon/nft-voting-plugin.git',
    documentation: 'https://docs.aragon.org/plugins/nft-voting',
  },
  tags: ['voting', 'nft', 'erc721', 'governance'],
};

export const PLUGIN_SETUP_METADATA = {
  name: 'NFT Voting Plugin Setup',
  description: 'Setup contract for the NFT Voting Plugin',
  avatar: 'https://github.com/aragon/nft-voting-plugin/raw/main/assets/icon.svg',
  links: {
    homepage: 'https://github.com/aragon/nft-voting-plugin',
    repository: 'https://github.com/aragon/nft-voting-plugin.git',
    documentation: 'https://docs.aragon.org/plugins/nft-voting',
  },
  tags: ['setup', 'voting', 'nft', 'erc721'],
};

export const RELEASE_METADATA = {
  name: 'NFT Voting Plugin v1.0.0',
  description: 'Initial release of the NFT Voting Plugin with support for ERC-721 token-based voting, role management, and configurable thresholds.',
  avatar: 'https://github.com/aragon/nft-voting-plugin/raw/main/assets/icon.svg',
  links: {
    homepage: 'https://github.com/aragon/nft-voting-plugin',
    repository: 'https://github.com/aragon/nft-voting-plugin.git',
    documentation: 'https://docs.aragon.org/plugins/nft-voting',
    changelog: 'https://github.com/aragon/nft-voting-plugin/blob/main/CHANGELOG.md',
  },
  tags: ['release', 'v1.0.0', 'voting', 'nft'],
};

export const BUILD_METADATA = {
  name: 'NFT Voting Plugin Build 1',
  description: 'First build of the NFT Voting Plugin with core functionality implemented.',
  avatar: 'https://github.com/aragon/nft-voting-plugin/raw/main/assets/icon.svg',
  links: {
    homepage: 'https://github.com/aragon/nft-voting-plugin',
    repository: 'https://github.com/aragon/nft-voting-plugin.git',
    documentation: 'https://docs.aragon.org/plugins/nft-voting',
  },
  tags: ['build', 'v1.0.0-build.1', 'voting', 'nft'],
};

// Permission IDs (keccak256 hashes)
export const PERMISSIONS = {
  CREATE_PROPOSAL_PERMISSION_ID: '0x3eb2b8e3a7c4c5b6d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7',
  EXECUTE_PROPOSAL_PERMISSION_ID: '0x4fc3c9f4b8d5c6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1',
  UPDATE_VOTING_SETTINGS_PERMISSION_ID: '0x5fd4d0f5c9e6d7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2',
  EXECUTE_PERMISSION_ID: '0x2dd8cc6b5a4e5c7b1f1b6c8e8b4c8e8b4c8e8b4c8e8b4c8e8b4c8e8b4c8e8b4c',
};

// Role IDs (keccak256 hashes)
export const ROLES = {
  MANAGER_ROLE: '0x241ecf16d79d0f8dbfb92cbc07fe17840425976cf0667f022fe9877caa831b08',
  COUNCIL_ROLE: '0x71840dc4906352362b0cdaf79870196c8e42acafade72d5d5a6d59291253ceb1',
};

// Default voting settings
export const DEFAULT_VOTING_SETTINGS = {
  votingMode: 0, // Standard
  supportThreshold: 500000, // 50%
  minParticipation: 250000, // 25%
  minDuration: 86400, // 1 day
  minProposerVotingPower: 1, // 1 NFT
};

// Network configurations
export const SUPPORTED_NETWORKS = {
  mainnet: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    supported: true,
  },
  polygon: {
    chainId: 137,
    name: 'Polygon',
    supported: true,
  },
  sepolia: {
    chainId: 11155111,
    name: 'Sepolia Testnet',
    supported: true,
  },
  polygonMumbai: {
    chainId: 80001,
    name: 'Polygon Mumbai',
    supported: true,
  },
};

// Plugin repository configuration
export const PLUGIN_REPO_CONFIG = {
  ensName: 'nft-voting-plugin.plugin.dao.eth',
  subdomain: 'nft-voting-plugin',
  alias: 'nft-voting',
  description: 'NFT-based voting plugin for Aragon DAOs',
};

// Installation parameters schema
export const INSTALLATION_PARAMS_SCHEMA = {
  type: 'object',
  properties: {
    nftContract: {
      type: 'string',
      pattern: '^0x[a-fA-F0-9]{40}$',
      description: 'The address of the ERC-721 contract used for voting',
    },
    votingSettings: {
      type: 'object',
      properties: {
        votingMode: {
          type: 'integer',
          minimum: 0,
          maximum: 2,
          description: 'Voting mode: 0=Standard, 1=EarlyExecution, 2=VoteReplacement',
        },
        supportThreshold: {
          type: 'integer',
          minimum: 0,
          maximum: 1000000,
          description: 'Support threshold in basis points (500000 = 50%)',
        },
        minParticipation: {
          type: 'integer',
          minimum: 0,
          maximum: 1000000,
          description: 'Minimum participation in basis points (250000 = 25%)',
        },
        minDuration: {
          type: 'integer',
          minimum: 3600,
          maximum: 31536000,
          description: 'Minimum voting duration in seconds',
        },
        minProposerVotingPower: {
          type: 'integer',
          minimum: 1,
          description: 'Minimum NFTs required to create a proposal',
        },
      },
      required: ['votingMode', 'supportThreshold', 'minParticipation', 'minDuration', 'minProposerVotingPower'],
    },
  },
  required: ['nftContract', 'votingSettings'],
};

export default {
  PLUGIN_METADATA,
  PLUGIN_SETUP_METADATA,
  RELEASE_METADATA,
  BUILD_METADATA,
  PERMISSIONS,
  ROLES,
  DEFAULT_VOTING_SETTINGS,
  SUPPORTED_NETWORKS,
  PLUGIN_REPO_CONFIG,
  INSTALLATION_PARAMS_SCHEMA,
};
