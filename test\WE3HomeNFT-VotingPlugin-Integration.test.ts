import {expect} from 'chai';
import {ethers} from 'hardhat';
import {SignerWithAddress} from '@nomiclabs/hardhat-ethers/signers';
import {
  WE3HomeNFT,
  NftVotingPlugin,
  NftVotingPluginSetup,
  MockDAO,
  RoleProposalHelper,
} from '../typechain';

describe('WE3HomeNFT + Voting Plugin Integration', function () {
  let we3NFT: WE3HomeNFT;
  let plugin: NftVotingPlugin;
  let pluginSetup: NftVotingPluginSetup;
  let dao: MockDAO;
  let roleHelper: RoleProposalHelper;
  
  let owner: Signer<PERSON>ithAddress;
  let backend: Signer<PERSON>ithAddress;
  let alice: SignerWithAddress;
  let bob: Signer<PERSON>ithAddress;
  let charlie: SignerWithAddress;
  let david: Signer<PERSON>ithAddress;
  let manager: Signer<PERSON>ithAddress;

  const votingSettings = {
    votingMode: 0, // Standard
    supportThreshold: 500000, // 50%
    minParticipation: 250000, // 25% 
    minDuration: 3600, // 1 hour for testing
    minProposerVotingPower: 1, // 1 NFT
  };

  before(async function () {
    [owner, backend, alice, bob, charlie, david, manager] = await ethers.getSigners();
    
    console.log('🏗️  Setting up WE3HomeNFT + Voting Plugin integration...');
    
    await deployContracts();
    await setupNFTs();
    await setupPlugin();
    
    console.log('✅ Integration test environment ready');
  });

  async function deployContracts() {
    // Deploy WE3HomeNFT
    const WE3HomeNFTFactory = await ethers.getContractFactory('WE3HomeNFT');
    we3NFT = await WE3HomeNFTFactory.deploy('https://api.we3home.com/metadata/');
    await we3NFT.deployed();
    await we3NFT.setBackendAddress(backend.address);
    console.log('✅ WE3HomeNFT deployed');

    // Deploy mock DAO
    const MockDAOFactory = await ethers.getContractFactory('MockDAO');
    dao = await MockDAOFactory.deploy();
    await dao.deployed();
    console.log('✅ Mock DAO deployed');

    // Deploy role helper
    const RoleHelperFactory = await ethers.getContractFactory('RoleProposalHelper');
    roleHelper = await RoleHelperFactory.deploy();
    await roleHelper.deployed();
    console.log('✅ Role Helper deployed');
  }

  async function setupNFTs() {
    // Mint WE3Home NFTs to DAO members
    await we3NFT.connect(backend).backendMint(alice.address);   // Token 1
    await we3NFT.connect(backend).backendMint(bob.address);     // Token 2
    await we3NFT.connect(backend).backendMint(charlie.address); // Token 3
    await we3NFT.connect(backend).backendMint(david.address);   // Token 4
    await we3NFT.connect(backend).backendMint(manager.address); // Token 5

    console.log('✅ WE3Home NFTs minted to DAO members');
    console.log(`   Total Supply: ${await we3NFT.totalSupply()}`);
  }

  async function setupPlugin() {
    // Deploy plugin setup
    const PluginSetupFactory = await ethers.getContractFactory('NftVotingPluginSetup');
    pluginSetup = await PluginSetupFactory.deploy();
    await pluginSetup.deployed();

    // Prepare installation with WE3HomeNFT
    const installationParams = await pluginSetup.encodeInstallationParams(
      we3NFT.address,
      votingSettings
    );

    const installationResult = await pluginSetup.prepareInstallation(
      dao.address,
      installationParams
    );

    plugin = await ethers.getContractAt('NftVotingPlugin', installationResult.plugin);
    console.log('✅ Voting Plugin deployed and configured with WE3HomeNFT');
  }

  describe('WE3HomeNFT Voting Mechanics', function () {
    it('should verify WE3HomeNFT is properly integrated', async function () {
      expect(await plugin.nftToken()).to.equal(we3NFT.address);
      expect(await plugin.totalVotingPower(0)).to.equal(5); // 5 NFTs minted
      
      // Each member has exactly 1 vote (1 NFT per wallet design)
      expect(await plugin.getVotingPower(alice.address, 0)).to.equal(1);
      expect(await plugin.getVotingPower(bob.address, 0)).to.equal(1);
      expect(await plugin.getVotingPower(charlie.address, 0)).to.equal(1);
      
      console.log('✅ WE3HomeNFT properly integrated with voting plugin');
    });

    it('should enforce one vote per member through NFT design', async function () {
      // WE3HomeNFT design: 1 NFT per wallet = 1 vote per member
      expect(await we3NFT.balanceOf(alice.address)).to.equal(1);
      expect(await we3NFT.hasMinted(alice.address)).to.be.true;
      
      // Cannot mint another NFT to same address
      await expect(we3NFT.connect(backend).backendMint(alice.address))
        .to.be.revertedWith('User already minted');
        
      console.log('✅ One vote per member enforced by NFT design');
    });
  });

  describe('DAO Governance with WE3Home NFTs', function () {
    let proposalId: number;

    it('should create a Manager appointment proposal', async function () {
      // Create manager appointment using role helper
      const managerActions = await roleHelper.createManagerAppointmentActions(
        dao.address,
        manager.address
      );

      const metadata = await roleHelper.createRoleProposalMetadata(
        0, // ProposalType.ManagerAppointment
        [manager.address],
        'Proposal to appoint a Manager for WE3Home DAO governance'
      );

      const startDate = Math.floor(Date.now() / 1000) + 60;
      const endDate = startDate + 3600;

      const tx = await plugin.connect(alice).createProposal(
        metadata,
        managerActions,
        0,
        startDate,
        endDate
      );

      proposalId = 1;
      expect(await plugin.proposalCounter()).to.equal(1);
      console.log('✅ Manager appointment proposal created');
    });

    it('should allow WE3Home NFT holders to vote', async function () {
      // Fast forward to voting start
      await ethers.provider.send('evm_increaseTime', [65]);
      await ethers.provider.send('evm_mine', []);

      // Each member votes with their WE3Home NFT
      await plugin.connect(alice).vote(proposalId, 1, 2);   // Alice votes Yes with token 1
      await plugin.connect(bob).vote(proposalId, 2, 2);     // Bob votes Yes with token 2
      await plugin.connect(charlie).vote(proposalId, 3, 3); // Charlie votes No with token 3
      await plugin.connect(david).vote(proposalId, 4, 1);   // David abstains with token 4

      const proposal = await plugin.getProposal(proposalId);
      expect(proposal.tally.yes).to.equal(2);
      expect(proposal.tally.no).to.equal(1);
      expect(proposal.tally.abstain).to.equal(1);

      console.log('✅ WE3Home NFT holders voted successfully');
      console.log(`   Results: ${proposal.tally.yes} Yes, ${proposal.tally.no} No, ${proposal.tally.abstain} Abstain`);
    });

    it('should calculate thresholds correctly with WE3Home NFTs', async function () {
      const proposal = await plugin.getProposal(proposalId);
      const totalVotingPower = await plugin.totalVotingPower(0);
      const totalVotes = proposal.tally.yes.add(proposal.tally.no).add(proposal.tally.abstain);
      
      // Participation: 4 votes out of 5 total NFTs = 80%
      const participationRate = totalVotes.mul(1000000).div(totalVotingPower);
      expect(participationRate).to.be.greaterThan(votingSettings.minParticipation);
      
      // Support: 2 yes out of 3 support votes (yes + no) = 66.67%
      const supportVotes = proposal.tally.yes.add(proposal.tally.no);
      const supportRate = proposal.tally.yes.mul(1000000).div(supportVotes);
      expect(supportRate).to.be.greaterThan(votingSettings.supportThreshold);

      console.log(`✅ Thresholds met - Participation: ${participationRate.toNumber() / 10000}%, Support: ${supportRate.toNumber() / 10000}%`);
    });

    it('should execute proposal and grant manager role', async function () {
      // Fast forward to end of voting period
      await ethers.provider.send('evm_increaseTime', [3601]);
      await ethers.provider.send('evm_mine', []);

      expect(await plugin.canExecute(proposalId)).to.be.true;
      expect(await plugin.hasProposalPassed(proposalId)).to.be.true;

      // Execute the proposal
      await plugin.execute(proposalId);

      // Verify execution
      const proposal = await plugin.getProposal(proposalId);
      expect(proposal.executed).to.be.true;

      // Verify DAO received the grant action
      const executedActionsCount = await dao.getExecutedActionsCount();
      expect(executedActionsCount).to.equal(1);

      console.log('✅ Proposal executed and manager role granted');
    });
  });

  describe('Council Member Appointment with WE3Home NFTs', function () {
    let councilProposalId: number;

    it('should create council member appointment proposal', async function () {
      const councilMembers = [bob.address, charlie.address];
      const councilActions = await roleHelper.createCouncilAppointmentActions(
        dao.address,
        councilMembers
      );

      const metadata = await roleHelper.createRoleProposalMetadata(
        1, // ProposalType.CouncilAppointment
        councilMembers,
        'Proposal to appoint Council Members for WE3Home DAO'
      );

      const startDate = Math.floor(Date.now() / 1000) + 60;
      const endDate = startDate + 3600;

      await plugin.connect(alice).createProposal(
        metadata,
        councilActions,
        0,
        startDate,
        endDate
      );

      councilProposalId = 2;
      expect(await plugin.proposalCounter()).to.equal(2);
      console.log('✅ Council appointment proposal created');
    });

    it('should handle voting with all WE3Home NFT holders', async function () {
      // Fast forward to voting start
      await ethers.provider.send('evm_increaseTime', [65]);
      await ethers.provider.send('evm_mine', []);

      // All 5 WE3Home NFT holders vote
      await plugin.connect(alice).vote(councilProposalId, 1, 2);   // Yes
      await plugin.connect(bob).vote(councilProposalId, 2, 2);     // Yes
      await plugin.connect(charlie).vote(councilProposalId, 3, 2); // Yes
      await plugin.connect(david).vote(councilProposalId, 4, 2);   // Yes
      await plugin.connect(manager).vote(councilProposalId, 5, 3); // No

      const proposal = await plugin.getProposal(councilProposalId);
      expect(proposal.tally.yes).to.equal(4);
      expect(proposal.tally.no).to.equal(1);
      expect(proposal.tally.abstain).to.equal(0);

      // 100% participation, 80% support
      console.log('✅ Full participation voting completed');
    });

    it('should execute council appointment with overwhelming support', async function () {
      // Fast forward to end of voting period
      await ethers.provider.send('evm_increaseTime', [3601]);
      await ethers.provider.send('evm_mine', []);

      expect(await plugin.canExecute(councilProposalId)).to.be.true;
      await plugin.execute(councilProposalId);

      // Verify multiple council member actions were executed
      const executedActionsCount = await dao.getExecutedActionsCount();
      expect(executedActionsCount).to.equal(3); // 1 manager + 2 council members

      console.log('✅ Council members appointed successfully');
    });
  });

  describe('Edge Cases with WE3Home NFT Design', function () {
    it('should prevent voting with non-owned WE3Home NFT', async function () {
      // Create test proposal
      const startDate = Math.floor(Date.now() / 1000) + 60;
      const endDate = startDate + 3600;

      await plugin.connect(alice).createProposal(
        ethers.utils.toUtf8Bytes('Test proposal'),
        [],
        0,
        startDate,
        endDate
      );

      const testProposalId = 3;

      // Fast forward to voting start
      await ethers.provider.send('evm_increaseTime', [65]);
      await ethers.provider.send('evm_mine', []);

      // Alice tries to vote with Bob's NFT
      await expect(plugin.connect(alice).vote(testProposalId, 2, 2))
        .to.be.revertedWith('VoteCastForbidden');

      console.log('✅ Non-owner voting prevention working');
    });

    it('should prevent double voting with same WE3Home NFT', async function () {
      const testProposalId = 3;

      // Alice votes with her NFT
      await plugin.connect(alice).vote(testProposalId, 1, 2);

      // Alice tries to vote again with same NFT
      await expect(plugin.connect(alice).vote(testProposalId, 1, 3))
        .to.be.revertedWith('TokenAlreadyVoted');

      console.log('✅ Double voting prevention working');
    });

    it('should handle proposals when new WE3Home NFTs are minted', async function () {
      // Mint a new WE3Home NFT to a new member
      const [newMember] = await ethers.getSigners();
      await we3NFT.connect(backend).backendMint(newMember.address); // Token 6

      // Verify voting power updated
      expect(await plugin.totalVotingPower(0)).to.equal(6);
      expect(await plugin.getVotingPower(newMember.address, 0)).to.equal(1);

      console.log('✅ Dynamic NFT supply handling working');
    });
  });

  after(async function () {
    console.log('\n📊 WE3HomeNFT + Voting Plugin Integration Summary:');
    console.log('='.repeat(60));
    console.log(`WE3Home NFTs Minted: ${await we3NFT.totalSupply()}`);
    console.log(`Total Proposals: ${await plugin.proposalCounter()}`);
    console.log(`DAO Actions Executed: ${await dao.getExecutedActionsCount()}`);
    console.log('NFT Integration: ✅ Perfect');
    console.log('One Vote Per Member: ✅ Enforced by NFT Design');
    console.log('Role Management: ✅ Working');
    console.log('Threshold Calculations: ✅ Accurate');
    console.log('='.repeat(60));
  });
});
