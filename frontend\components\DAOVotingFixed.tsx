import React, { useState, useEffect } from 'react';

// Declare global ethereum interface
declare global {
  interface Window {
    ethereum?: any;
  }
}

// Contract addresses on Polygon Amoy Testnet
const CONTRACTS = {
  NFT_ADDRESS: '******************************************',
  VOTING_ADDRESS: '******************************************',
  NETWORK: {
    chainId: '0x13882', // 80002 in hex (Polygon Amoy)
    chainName: 'Polygon Amoy Testnet',
    rpcUrls: ['https://polygon-amoy.infura.io/v3/********************************'],
    blockExplorerUrls: ['https://amoy.polygonscan.com/'],
    nativeCurrency: {
      name: 'MA<PERSON><PERSON>',
      symbol: 'MATIC',
      decimals: 18
    }
  }
};

const DAOVotingFixed = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [account, setAccount] = useState('');
  const [loading, setLoading] = useState(false);

  // Connect to wallet
  const connectWallet = async () => {
    try {
      if (!window.ethereum) {
        alert('Please install MetaMask!');
        return;
      }

      setLoading(true);
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts'
      });
      
      // Switch to Polygon Amoy if needed
      try {
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: CONTRACTS.NETWORK.chainId }]
        });
      } catch (error: any) {
        if (error.code === 4902) {
          await window.ethereum.request({
            method: 'wallet_addEthereumChain',
            params: [CONTRACTS.NETWORK]
          });
        }
      }

      setAccount(accounts[0]);
      setIsConnected(true);
    } catch (error) {
      console.error('Error connecting wallet:', error);
      alert('Failed to connect wallet');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 rounded-2xl p-8 mb-8 text-white shadow-2xl">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">
            WE3Home DAO Voting
          </h1>
          <p className="text-purple-100 text-lg">Decentralized governance powered by your WE3Home NFTs</p>
        </div>
        
        {/* Stats Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 text-center">
            <div className="text-2xl font-bold">1 NFT = 1 Vote</div>
            <div className="text-sm opacity-90">Democratic Governance</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 text-center">
            <div className="text-2xl font-bold">5,200</div>
            <div className="text-sm opacity-90">Max DAO Members</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 text-center">
            <div className="text-2xl font-bold">Testnet</div>
            <div className="text-sm opacity-90">Safe Environment</div>
          </div>
        </div>
      </div>

      {/* Connection Status */}
      <div className="mb-8">
        {!isConnected ? (
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Connect Your Wallet</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                Connect your wallet to participate in DAO governance and vote with your WE3Home NFTs
              </p>
              <button
                onClick={connectWallet}
                disabled={loading}
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold disabled:opacity-50"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                    Connecting...
                  </div>
                ) : (
                  '🦊 Connect MetaMask'
                )}
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-green-700 font-semibold text-lg">✅ Wallet Connected</p>
                  <p className="text-gray-600">{account.slice(0, 6)}...{account.slice(-4)}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-purple-600">Ready</div>
                <div className="text-sm text-gray-500">to Vote</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Voting Interface Placeholder */}
      {isConnected && (
        <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100">
          <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <svg className="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            DAO Proposals
          </h2>
          
          <div className="bg-blue-50 rounded-xl p-6 border border-blue-200 text-center">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Voting Interface Coming Soon</h3>
            <p className="text-blue-700 mb-4">
              The full voting interface is being finalized. You can interact with the contracts directly for now.
            </p>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold mb-2">NFT Contract</h4>
                <a 
                  href={`https://amoy.polygonscan.com/address/${CONTRACTS.NFT_ADDRESS}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline font-mono break-all"
                >
                  {CONTRACTS.NFT_ADDRESS}
                </a>
              </div>
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold mb-2">Voting Contract</h4>
                <a 
                  href={`https://amoy.polygonscan.com/address/${CONTRACTS.VOTING_ADDRESS}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline font-mono break-all"
                >
                  {CONTRACTS.VOTING_ADDRESS}
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Contract Info */}
      <div className="mt-12">
        <div className="bg-gradient-to-r from-gray-900 to-blue-900 rounded-2xl p-8 text-white shadow-2xl">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Smart Contract Information</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="font-semibold text-lg mb-4">WE3Home NFT Contract</h4>
                <div className="space-y-2 text-sm">
                  <div>Contract Address:</div>
                  <div className="font-mono text-blue-300 break-all">{CONTRACTS.NFT_ADDRESS}</div>
                  <div>Purpose: NFT membership tokens (1 NFT = 1 vote)</div>
                </div>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h4 className="font-semibold text-lg mb-4">DAO Voting Contract</h4>
                <div className="space-y-2 text-sm">
                  <div>Contract Address:</div>
                  <div className="font-mono text-blue-300 break-all">{CONTRACTS.VOTING_ADDRESS}</div>
                  <div>Purpose: Governance voting and proposal management</div>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-yellow-900/30 border border-yellow-600/50 rounded-xl">
              <div className="flex items-center justify-center">
                <svg className="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="text-yellow-200 font-medium">
                  Running on Polygon Amoy Testnet - Safe testing environment
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DAOVotingFixed;
