import {ethers} from 'hardhat';
import {WE3HomeNFT} from '../typechain';

async function main() {
  console.log('🚀 Deploying WE3HomeNFT...\n');

  const [deployer] = await ethers.getSigners();
  console.log('Deploying with account:', deployer.address);
  console.log('Account balance:', ethers.utils.formatEther(await deployer.getBalance()), 'ETH\n');

  // Get network information
  const network = await ethers.provider.getNetwork();
  console.log('Network:', network.name);
  console.log('Chain ID:', network.chainId);
  console.log('Block number:', await ethers.provider.getBlockNumber(), '\n');

  // Configuration
  const baseURI = process.env.NFT_BASE_URI || 'https://api.we3home.com/metadata/';
  const backendAddress = process.env.BACKEND_ADDRESS || deployer.address;

  console.log('📋 Deployment Configuration:');
  console.log('='.repeat(50));
  console.log(`Base URI: ${baseURI}`);
  console.log(`Backend Address: ${backendAddress}`);
  console.log('='.repeat(50));

  // Deploy WE3HomeNFT
  console.log('\n📦 Deploying WE3HomeNFT...');
  const WE3HomeNFTFactory = await ethers.getContractFactory('WE3HomeNFT');
  const we3NFT: WE3HomeNFT = await WE3HomeNFTFactory.deploy(baseURI);
  await we3NFT.deployed();

  console.log(`✅ WE3HomeNFT deployed at: ${we3NFT.address}`);
  console.log('Gas used:', (await we3NFT.deployTransaction.wait()).gasUsed.toString());

  // Set backend address if different from deployer
  if (backendAddress !== deployer.address) {
    console.log('\n🔧 Setting backend address...');
    const tx = await we3NFT.setBackendAddress(backendAddress);
    await tx.wait();
    console.log(`✅ Backend address set to: ${backendAddress}`);
  }

  // Verify contract state
  console.log('\n🔍 Verifying contract state...');
  console.log(`Name: ${await we3NFT.name()}`);
  console.log(`Symbol: ${await we3NFT.symbol()}`);
  console.log(`Max Supply: ${await we3NFT.maxSupply()}`);
  console.log(`Max Per Wallet: ${await we3NFT.maxPerWallet()}`);
  console.log(`Total Supply: ${await we3NFT.totalSupply()}`);
  console.log(`Base URI: ${await we3NFT.baseTokenURI()}`);
  console.log(`Backend Address: ${await we3NFT.backendAddress()}`);
  console.log(`Owner: ${await we3NFT.owner()}`);

  // Save deployment information
  const deploymentInfo = {
    network: {
      name: network.name,
      chainId: network.chainId,
    },
    contract: {
      name: 'WE3HomeNFT',
      address: we3NFT.address,
      transactionHash: we3NFT.deployTransaction.hash,
      deployer: deployer.address,
      backendAddress: await we3NFT.backendAddress(),
      baseURI: await we3NFT.baseTokenURI(),
    },
    timestamp: new Date().toISOString(),
  };

  // Write deployment info to file
  const fs = require('fs');
  const path = require('path');
  
  const deploymentsDir = path.join(__dirname, '..', 'deployments');
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, {recursive: true});
  }

  const deploymentFile = path.join(
    deploymentsDir,
    `we3home-nft-${network.name}-${Date.now()}.json`
  );
  
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`\n💾 Deployment info saved to: ${deploymentFile}`);

  // Display next steps
  console.log('\n🎯 Next Steps:');
  console.log('1. Verify contract on block explorer:');
  console.log(`   npx hardhat verify --network ${network.name} ${we3NFT.address} "${baseURI}"`);
  console.log('2. Set up backend minting permissions');
  console.log('3. Configure metadata API endpoints');
  console.log('4. Test minting functionality');
  console.log('5. Deploy voting plugin with this NFT contract');

  if (network.chainId !== 31337) { // Not local hardhat network
    console.log('\n📚 Verification Command:');
    console.log(`npx hardhat verify --network ${network.name} ${we3NFT.address} "${baseURI}"`);
  }

  return {
    address: we3NFT.address,
    network: network.name,
    chainId: network.chainId,
    deployer: deployer.address,
    backendAddress: await we3NFT.backendAddress(),
  };
}

// Execute deployment if this script is run directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Deployment failed:', error);
      process.exit(1);
    });
}

export default main;
