import {ethers} from 'hardhat';
import deployNFT from './deploy-nft';

async function main() {
  console.log('🌐 Testnet Deployment Script');
  console.log('============================\n');

  const [deployer] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log('Deployment Information:');
  console.log(`Network: ${network.name} (${network.chainId})`);
  console.log(`Deployer: ${deployer.address}`);
  console.log(`Balance: ${ethers.utils.formatEther(await deployer.getBalance())} ETH\n`);

  // Verify we're on a testnet
  const testnets = [11155111, 80001, 421614, 84532, 11155420]; // Sepolia, Mumbai, Arbitrum Sepolia, Base Sepolia, Optimism Sepolia
  if (!testnets.includes(network.chainId)) {
    console.warn('⚠️  Warning: Not deploying on a recognized testnet!');
    console.log('Recognized testnets: Sepolia (11155111), Mumbai (80001), Arbitrum Sepolia (421614), Base Sepolia (84532), Optimism Sepolia (11155420)');
    
    // Ask for confirmation in non-testnet environments
    if (network.chainId === 1 || network.chainId === 137 || network.chainId === 42161) {
      throw new Error('❌ Refusing to deploy on mainnet without explicit mainnet script');
    }
  }

  console.log('📋 Testnet Configuration:');
  console.log('='.repeat(50));
  console.log(`NFT Base URI: ${process.env.NFT_BASE_URI || 'https://api.we3home.com/metadata/'}`);
  console.log(`Backend Address: ${process.env.BACKEND_ADDRESS || deployer.address}`);
  console.log(`Gas Price: ${process.env.GAS_PRICE || 'auto'}`);
  console.log('='.repeat(50));

  let nftDeployment: any;
  let pluginDeployment: any;

  try {
    // Step 1: Deploy WE3HomeNFT
    console.log('\n📦 Step 1: Deploying WE3HomeNFT');
    console.log('-'.repeat(40));
    nftDeployment = await deployNFT();
    console.log('✅ WE3HomeNFT deployed successfully\n');

    // Step 2: Deploy Voting Plugin (if enabled)
    if (process.env.DEPLOY_PLUGIN === 'true') {
      console.log('📦 Step 2: Deploying Voting Plugin');
      console.log('-'.repeat(40));
      
      // Import and run plugin deployment
      const {execSync} = require('child_process');
      const pluginResult = execSync(
        'cd aragon-nft-voting-plugin && npm run deploy',
        {encoding: 'utf8', stdio: 'inherit'}
      );
      
      console.log('✅ Voting Plugin deployed successfully\n');
    } else {
      console.log('⏭️  Step 2: Skipping plugin deployment (DEPLOY_PLUGIN not set to true)\n');
    }

    // Step 3: Mint test NFTs (if enabled)
    if (process.env.MINT_TEST_NFTS === 'true') {
      console.log('🎨 Step 3: Minting Test NFTs');
      console.log('-'.repeat(40));
      
      const we3NFT = await ethers.getContractAt('WE3HomeNFT', nftDeployment.address);
      const testAddresses = process.env.TEST_ADDRESSES?.split(',') || [deployer.address];
      
      for (let i = 0; i < Math.min(testAddresses.length, 5); i++) {
        const address = testAddresses[i].trim();
        if (ethers.utils.isAddress(address)) {
          try {
            const tx = await we3NFT.backendMint(address);
            await tx.wait();
            console.log(`✅ Minted NFT to: ${address}`);
          } catch (error) {
            console.log(`⚠️  Failed to mint to ${address}: ${error}`);
          }
        }
      }
      console.log('✅ Test NFT minting completed\n');
    } else {
      console.log('⏭️  Step 3: Skipping test NFT minting (MINT_TEST_NFTS not set to true)\n');
    }

    // Step 4: Verify contracts (if enabled)
    if (process.env.VERIFY_CONTRACTS === 'true' && network.chainId !== 31337) {
      console.log('🔍 Step 4: Verifying Contracts');
      console.log('-'.repeat(40));
      
      try {
        const baseURI = process.env.NFT_BASE_URI || 'https://api.we3home.com/metadata/';
        const {execSync} = require('child_process');
        
        const verifyCommand = `npx hardhat verify --network ${network.name} ${nftDeployment.address} "${baseURI}"`;
        console.log(`Running: ${verifyCommand}`);
        
        execSync(verifyCommand, {encoding: 'utf8', stdio: 'inherit'});
        console.log('✅ Contract verification completed\n');
      } catch (error) {
        console.log('⚠️  Contract verification failed (this is normal if already verified)\n');
      }
    } else {
      console.log('⏭️  Step 4: Skipping contract verification\n');
    }

    // Final summary
    console.log('🎉 Testnet Deployment Complete!');
    console.log('='.repeat(50));
    console.log(`WE3HomeNFT: ${nftDeployment.address}`);
    console.log(`Network: ${nftDeployment.network} (${nftDeployment.chainId})`);
    console.log(`Deployer: ${nftDeployment.deployer}`);
    console.log(`Backend: ${nftDeployment.backendAddress}`);
    console.log('='.repeat(50));

    // Save comprehensive deployment info
    const deploymentSummary = {
      timestamp: new Date().toISOString(),
      network: {
        name: network.name,
        chainId: network.chainId,
      },
      deployer: deployer.address,
      contracts: {
        we3HomeNFT: nftDeployment,
        votingPlugin: pluginDeployment || null,
      },
      configuration: {
        baseURI: process.env.NFT_BASE_URI || 'https://api.we3home.com/metadata/',
        backendAddress: process.env.BACKEND_ADDRESS || deployer.address,
        deployPlugin: process.env.DEPLOY_PLUGIN === 'true',
        mintTestNFTs: process.env.MINT_TEST_NFTS === 'true',
        verifyContracts: process.env.VERIFY_CONTRACTS === 'true',
      },
    };

    const fs = require('fs');
    const path = require('path');
    
    const deploymentsDir = path.join(__dirname, '..', 'deployments');
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, {recursive: true});
    }

    const summaryFile = path.join(
      deploymentsDir,
      `testnet-deployment-${network.name}-${Date.now()}.json`
    );
    
    fs.writeFileSync(summaryFile, JSON.stringify(deploymentSummary, null, 2));
    console.log(`\n💾 Deployment summary saved to: ${summaryFile}`);

    console.log('\n🎯 Next Steps:');
    console.log('1. Update your frontend/backend with the new contract address');
    console.log('2. Configure your backend to use the deployed NFT contract');
    console.log('3. Test minting functionality through your application');
    console.log('4. Set up monitoring and alerts for the contracts');
    console.log('5. Prepare for mainnet deployment when ready');

    console.log('\n📚 Useful Commands:');
    console.log(`# Interact with your NFT contract:`);
    console.log(`npx hardhat console --network ${network.name}`);
    console.log(`# Run integration tests:`);
    console.log(`npm run test:integration`);
    console.log(`# Deploy voting plugin separately:`);
    console.log(`cd aragon-nft-voting-plugin && npm run deploy`);

    return deploymentSummary;

  } catch (error) {
    console.error('❌ Testnet deployment failed:', error);
    throw error;
  }
}

// Execute deployment if this script is run directly
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n✅ Testnet deployment completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Testnet deployment failed:', error);
      process.exit(1);
    });
}

export default main;
