'use client';

import React, { useState, useEffect } from 'react';
import TransactionHistory from '../../components/TransactionHistory';

const CONTRACTS = {
  NFT_ADDRESS: '0x8B84ca52F8105b4b62aFef0aB3eAF19E8417076F',
  VOTING_ADDRESS: '0x7A490c2c1e7845A175F303A21F142b261Ee4e7B7',
  NETWORK: 'Polygon Amoy Testnet',
  EXPLORER: 'https://amoy.polygonscan.com'
};

export default function ContractsPage() {
  const [copied, setCopied] = useState('');

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    setCopied(type);
    setTimeout(() => setCopied(''), 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mr-3">
                <span className="text-white font-bold text-lg">W3</span>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                WE3Home
              </h1>
            </div>
            <nav className="flex space-x-8">
              <a href="/" className="text-gray-600 hover:text-purple-600 transition-colors font-medium">
                Home
              </a>
              <a href="/dao" className="text-gray-600 hover:text-purple-600 transition-colors font-medium">
                DAO
              </a>
              <a href="/contracts" className="text-purple-600 font-semibold border-b-2 border-purple-600 pb-1">
                Contracts
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center text-white">
          <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
            Smart Contract
            <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
              Transparency
            </span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-4xl mx-auto leading-relaxed opacity-90">
            All our smart contracts are deployed on blockchain and fully verifiable. 
            Complete transparency for our clients and community.
          </p>
          
          <div className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-sm rounded-full border border-white/30">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.012-3H20m-7 7v-7a7 7 0 00-14 0v7m14 0H5m14 0v4a2 2 0 01-2 2H7a2 2 0 01-2-2v-4" />
            </svg>
            <span className="font-semibold">100% Verifiable & Transparent</span>
          </div>
        </div>
      </section>

      {/* Contract Information */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          
          {/* Network Info */}
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 mb-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">Deployment Information</h2>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
                  <h3 className="text-lg font-semibold text-blue-800 mb-2">Network</h3>
                  <p className="text-blue-700 font-medium">{CONTRACTS.NETWORK}</p>
                  <p className="text-sm text-blue-600 mt-1">Chain ID: 80002</p>
                </div>
                <div className="bg-green-50 rounded-xl p-6 border border-green-200">
                  <h3 className="text-lg font-semibold text-green-800 mb-2">Status</h3>
                  <p className="text-green-700 font-medium">✅ Live & Verified</p>
                  <p className="text-sm text-green-600 mt-1">Fully operational</p>
                </div>
                <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
                  <h3 className="text-lg font-semibold text-purple-800 mb-2">Transparency</h3>
                  <p className="text-purple-700 font-medium">🔍 100% Open</p>
                  <p className="text-sm text-purple-600 mt-1">Source code verified</p>
                </div>
              </div>
            </div>
          </div>

          {/* NFT Contract */}
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 mb-8">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">WE3HomeNFT Contract</h2>
                <p className="text-gray-600">ERC-721 NFT Contract for DAO Membership</p>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Contract Address</label>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 bg-gray-100 p-3 rounded-lg font-mono text-sm break-all">
                      {CONTRACTS.NFT_ADDRESS}
                    </code>
                    <button
                      onClick={() => copyToClipboard(CONTRACTS.NFT_ADDRESS, 'nft')}
                      className="bg-purple-600 text-white p-2 rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      {copied === 'nft' ? '✓' : '📋'}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Contract Features</label>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      ERC-721 Standard Compliant
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      1 NFT per wallet (DAO membership)
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      5,200 maximum supply
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Backend-controlled minting
                    </li>
                  </ul>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Verification Links</label>
                  <div className="space-y-2">
                    <a
                      href={`${CONTRACTS.EXPLORER}/address/${CONTRACTS.NFT_ADDRESS}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-blue-50 border border-blue-200 rounded-lg p-3 hover:bg-blue-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-blue-700 font-medium">View on Polygonscan</span>
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </a>
                    <a
                      href={`${CONTRACTS.EXPLORER}/address/${CONTRACTS.NFT_ADDRESS}#code`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-green-50 border border-green-200 rounded-lg p-3 hover:bg-green-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-green-700 font-medium">View Source Code</span>
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Live Statistics</label>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-purple-600">1</div>
                        <div className="text-xs text-gray-500">NFTs Minted</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">5,199</div>
                        <div className="text-xs text-gray-500">Available</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Voting Contract */}
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">DAO Voting Contract</h2>
                <p className="text-gray-600">Governance and Proposal Management</p>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Contract Address</label>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 bg-gray-100 p-3 rounded-lg font-mono text-sm break-all">
                      {CONTRACTS.VOTING_ADDRESS}
                    </code>
                    <button
                      onClick={() => copyToClipboard(CONTRACTS.VOTING_ADDRESS, 'voting')}
                      className="bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors"
                    >
                      {copied === 'voting' ? '✓' : '📋'}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Voting Features</label>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      NFT-based voting (1 NFT = 1 vote)
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Proposal creation and management
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Configurable voting thresholds
                    </li>
                    <li className="flex items-center">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                      Double voting prevention
                    </li>
                  </ul>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Verification Links</label>
                  <div className="space-y-2">
                    <a
                      href={`${CONTRACTS.EXPLORER}/address/${CONTRACTS.VOTING_ADDRESS}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-blue-50 border border-blue-200 rounded-lg p-3 hover:bg-blue-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-blue-700 font-medium">View on Polygonscan</span>
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </a>
                    <a
                      href={`${CONTRACTS.EXPLORER}/address/${CONTRACTS.VOTING_ADDRESS}#code`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-green-50 border border-green-200 rounded-lg p-3 hover:bg-green-100 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-green-700 font-medium">View Source Code</span>
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </a>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Governance Stats</label>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-green-600">0</div>
                        <div className="text-xs text-gray-500">Proposals</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">1</div>
                        <div className="text-xs text-gray-500">Voters</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Transaction History */}
          <div className="mt-8">
            <TransactionHistory />
          </div>

        </div>
      </section>

      {/* Transparency Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Why Blockchain Transparency Matters</h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-blue-800 mb-2">Full Visibility</h3>
              <p className="text-blue-700">Every transaction, vote, and contract interaction is publicly visible and verifiable.</p>
            </div>

            <div className="bg-green-50 rounded-xl p-6 border border-green-200">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.012-3H20m-7 7v-7a7 7 0 00-14 0v7m14 0H5m14 0v4a2 2 0 01-2 2H7a2 2 0 01-2-2v-4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-green-800 mb-2">Immutable Records</h3>
              <p className="text-green-700">All records are permanently stored on blockchain and cannot be altered or deleted.</p>
            </div>

            <div className="bg-purple-50 rounded-xl p-6 border border-purple-200">
              <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-purple-800 mb-2">Community Trust</h3>
              <p className="text-purple-700">Open source code and public verification builds trust with your community.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
