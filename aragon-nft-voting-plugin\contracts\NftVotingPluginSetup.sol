// SPDX-License-Identifier: AGPL-3.0-or-later
pragma solidity 0.8.17;

import {PluginSetup, IPluginSetup} from "@aragon/osx/framework/plugin/setup/PluginSetup.sol";
import {PermissionLib} from "@aragon/osx/core/permission/PermissionLib.sol";
import {IDAO} from "@aragon/osx/core/dao/IDAO.sol";
import {IERC721} from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import {NftVotingPlugin} from "./NftVotingPlugin.sol";

/// @title NftVotingPluginSetup
/// <AUTHOR>
/// @notice The setup contract for the NFT Voting Plugin
/// @dev This contract handles the installation, uninstallation, and upgrade of the NFT Voting Plugin
contract NftVotingPluginSetup is PluginSetup {
    /// @notice The address of the NFT Voting Plugin implementation
    address private immutable nftVotingPluginImplementation;

    /// @notice Thrown when the NFT contract address is invalid
    error InvalidNftContract();

    /// @notice Thrown when the voting settings are invalid
    error InvalidVotingSettings();

    /// @notice The struct for the plugin installation parameters
    /// @param nftContract The address of the ERC-721 contract used for voting
    /// @param votingSettings The voting settings for the plugin
    struct InstallationParams {
        address nftContract;
        NftVotingPlugin.VotingSettings votingSettings;
    }

    /// @notice Constructor
    constructor() {
        nftVotingPluginImplementation = address(new NftVotingPlugin(IDAO(address(0)), IERC721(address(0)), 
            NftVotingPlugin.VotingSettings({
                votingMode: NftVotingPlugin.VotingMode.Standard,
                supportThreshold: 500000, // 50%
                minParticipation: 250000, // 25%
                minDuration: 86400, // 1 day
                minProposerVotingPower: 1 // 1 NFT
            })
        ));
    }

    /// @inheritdoc IPluginSetup
    function prepareInstallation(
        address _dao,
        bytes calldata _data
    ) external returns (address plugin, PreparedSetupData memory preparedSetupData) {
        // Decode installation parameters
        InstallationParams memory params = abi.decode(_data, (InstallationParams));

        // Validate NFT contract
        if (params.nftContract == address(0)) {
            revert InvalidNftContract();
        }

        // Validate voting settings
        if (params.votingSettings.supportThreshold > 1000000 || 
            params.votingSettings.minParticipation > 1000000 ||
            params.votingSettings.minDuration < 1 hours ||
            params.votingSettings.minDuration > 365 days) {
            revert InvalidVotingSettings();
        }

        // Deploy the plugin
        plugin = createERC1967Proxy(
            nftVotingPluginImplementation,
            abi.encodeWithSelector(
                NftVotingPlugin.initialize.selector,
                _dao,
                params.nftContract,
                params.votingSettings
            )
        );

        // Prepare permissions
        PermissionLib.MultiTargetPermission[] memory permissions = new PermissionLib.MultiTargetPermission[](4);

        // Grant CREATE_PROPOSAL_PERMISSION to the DAO
        permissions[0] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Grant,
            where: plugin,
            who: _dao,
            condition: PermissionLib.NO_CONDITION,
            permissionId: NftVotingPlugin(plugin).CREATE_PROPOSAL_PERMISSION_ID()
        });

        // Grant EXECUTE_PROPOSAL_PERMISSION to the plugin (so it can execute proposals)
        permissions[1] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Grant,
            where: _dao,
            who: plugin,
            condition: PermissionLib.NO_CONDITION,
            permissionId: keccak256("EXECUTE_PERMISSION")
        });

        // Grant UPDATE_VOTING_SETTINGS_PERMISSION to the DAO
        permissions[2] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Grant,
            where: plugin,
            who: _dao,
            condition: PermissionLib.NO_CONDITION,
            permissionId: NftVotingPlugin(plugin).UPDATE_VOTING_SETTINGS_PERMISSION_ID()
        });

        // Grant EXECUTE_PROPOSAL_PERMISSION to the DAO (so DAO can execute proposals manually)
        permissions[3] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Grant,
            where: plugin,
            who: _dao,
            condition: PermissionLib.NO_CONDITION,
            permissionId: NftVotingPlugin(plugin).EXECUTE_PROPOSAL_PERMISSION_ID()
        });

        preparedSetupData.permissions = permissions;
    }

    /// @inheritdoc IPluginSetup
    function prepareUninstallation(
        address _dao,
        SetupPayload calldata _payload
    ) external pure returns (PermissionLib.MultiTargetPermission[] memory permissions) {
        // Prepare permissions to revoke
        permissions = new PermissionLib.MultiTargetPermission[](4);

        // Revoke CREATE_PROPOSAL_PERMISSION from the DAO
        permissions[0] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Revoke,
            where: _payload.plugin,
            who: _dao,
            condition: PermissionLib.NO_CONDITION,
            permissionId: NftVotingPlugin(_payload.plugin).CREATE_PROPOSAL_PERMISSION_ID()
        });

        // Revoke EXECUTE_PERMISSION from the plugin
        permissions[1] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Revoke,
            where: _dao,
            who: _payload.plugin,
            condition: PermissionLib.NO_CONDITION,
            permissionId: keccak256("EXECUTE_PERMISSION")
        });

        // Revoke UPDATE_VOTING_SETTINGS_PERMISSION from the DAO
        permissions[2] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Revoke,
            where: _payload.plugin,
            who: _dao,
            condition: PermissionLib.NO_CONDITION,
            permissionId: NftVotingPlugin(_payload.plugin).UPDATE_VOTING_SETTINGS_PERMISSION_ID()
        });

        // Revoke EXECUTE_PROPOSAL_PERMISSION from the DAO
        permissions[3] = PermissionLib.MultiTargetPermission({
            operation: PermissionLib.Operation.Revoke,
            where: _payload.plugin,
            who: _dao,
            condition: PermissionLib.NO_CONDITION,
            permissionId: NftVotingPlugin(_payload.plugin).EXECUTE_PROPOSAL_PERMISSION_ID()
        });
    }

    /// @inheritdoc IPluginSetup
    function prepareUpdate(
        address _dao,
        uint16 _fromBuild,
        SetupPayload calldata _payload
    )
        external
        pure
        returns (
            bytes memory initData,
            PermissionLib.MultiTargetPermission[] memory permissions
        )
    {
        // Silence unused parameter warnings
        (_dao, _fromBuild, _payload);
        
        // For now, no updates are supported
        // In future versions, this would handle plugin upgrades
        permissions = new PermissionLib.MultiTargetPermission[](0);
        initData = "";
    }

    /// @inheritdoc IPluginSetup
    function implementation() external view returns (address) {
        return nftVotingPluginImplementation;
    }

    /// @notice Encodes the installation parameters
    /// @param _nftContract The address of the ERC-721 contract
    /// @param _votingSettings The voting settings
    /// @return The encoded installation parameters
    function encodeInstallationParams(
        address _nftContract,
        NftVotingPlugin.VotingSettings calldata _votingSettings
    ) external pure returns (bytes memory) {
        return abi.encode(InstallationParams({
            nftContract: _nftContract,
            votingSettings: _votingSettings
        }));
    }
}
