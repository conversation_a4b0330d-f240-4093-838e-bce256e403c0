'use client';

import React from 'react';

const CONTRACTS = {
  NFT_ADDRESS: '0x8B84ca52F8105b4b62aFef0aB3eAF19E8417076F',
  VOTING_ADDRESS: '0x7A490c2c1e7845A175F303A21F142b261Ee4e7B7',
  EXPLORER: 'https://amoy.polygonscan.com'
};

// Mock transaction data - in real app, this would come from blockchain API
const RECENT_TRANSACTIONS = [
  {
    hash: '0x1234567890abcdef1234567890abcdef12345678',
    type: 'NFT Mint',
    from: '0x173B513494fb5162A9cd8A380E727B7F91cBfE9d',
    to: '0x7b40f2DBCfC1dBdD638b89290BAea9f090f25E51',
    timestamp: '2024-01-15 14:30:25',
    status: 'Success',
    contract: 'NFT'
  },
  {
    hash: '0xabcdef1234567890abcdef1234567890abcdef12',
    type: 'Contract Deploy',
    from: '0x173B513494fb5162A9cd8A380E727B7F91cBfE9d',
    to: CONTRACTS.NFT_ADDRESS,
    timestamp: '2024-01-15 14:25:10',
    status: 'Success',
    contract: 'NFT'
  },
  {
    hash: '0x567890abcdef1234567890abcdef1234567890ab',
    type: 'Contract Deploy',
    from: '0x173B513494fb5162A9cd8A380E727B7F91cBfE9d',
    to: CONTRACTS.VOTING_ADDRESS,
    timestamp: '2024-01-15 14:20:45',
    status: 'Success',
    contract: 'Voting'
  }
];

const TransactionHistory = () => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'NFT Mint':
        return (
          <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
        );
      case 'Contract Deploy':
        return (
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      case 'Vote Cast':
        return (
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
        );
      default:
        return (
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
    }
  };

  const getContractBadge = (contract: string) => {
    if (contract === 'NFT') {
      return <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs font-semibold rounded-full">NFT</span>;
    } else if (contract === 'Voting') {
      return <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">DAO</span>;
    }
    return <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-semibold rounded-full">Other</span>;
  };

  return (
    <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-gray-800 flex items-center">
          <svg className="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Recent Transactions
        </h3>
        <a
          href={`${CONTRACTS.EXPLORER}/address/${CONTRACTS.NFT_ADDRESS}`}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
        >
          View All
          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </a>
      </div>

      <div className="space-y-4">
        {RECENT_TRANSACTIONS.map((tx, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  {getTypeIcon(tx.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900">{tx.type}</p>
                    {getContractBadge(tx.contract)}
                  </div>
                  <div className="flex items-center space-x-4 mt-1">
                    <p className="text-xs text-gray-500">
                      From: {tx.from.slice(0, 6)}...{tx.from.slice(-4)}
                    </p>
                    <p className="text-xs text-gray-500">
                      To: {tx.to.slice(0, 6)}...{tx.to.slice(-4)}
                    </p>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    ✓ {tx.status}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">{tx.timestamp}</p>
              </div>
            </div>
            
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center justify-between">
                <code className="text-xs text-gray-600 font-mono">
                  {tx.hash.slice(0, 20)}...{tx.hash.slice(-8)}
                </code>
                <a
                  href={`${CONTRACTS.EXPLORER}/tx/${tx.hash}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 text-xs font-medium flex items-center"
                >
                  View Details
                  <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 className="text-sm font-semibold text-blue-800">Blockchain Transparency</h4>
            <p className="text-xs text-blue-700">
              All transactions are permanently recorded on the blockchain and publicly verifiable.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionHistory;
