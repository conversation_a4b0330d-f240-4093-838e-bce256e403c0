// SPDX-License-Identifier: AGPL-3.0-or-later
pragma solidity 0.8.17;

import {<PERSON><PERSON><PERSON>} from "@aragon/osx/core/dao/IDAO.sol";
import {PermissionLib} from "@aragon/osx/core/permission/PermissionLib.sol";

/// @title RoleProposalHelper
/// <AUTHOR>
/// @notice Helper contract for creating role-based proposals (Manager and Council appointments)
/// @dev This contract provides utilities to create standardized role assignment proposals
contract RoleProposalHelper {
    /// @notice The role identifier for Manager role
    bytes32 public constant MANAGER_ROLE = keccak256("MANAGER_ROLE");

    /// @notice The role identifier for Council Member role
    bytes32 public constant COUNCIL_ROLE = keccak256("COUNCIL_ROLE");

    /// @notice The permission identifier for executing actions
    bytes32 public constant EXECUTE_PERMISSION = keccak256("EXECUTE_PERMISSION");

    /// @notice Enum for different proposal types
    enum ProposalType {
        ManagerAppointment,
        CouncilAppointment,
        ManagerRemoval,
        CouncilRemoval,
        Custom
    }

    /// @notice Struct for role proposal parameters
    /// @param proposalType The type of proposal
    /// @param targetAddress The address to grant/revoke the role to/from
    /// @param role The role to be granted/revoked
    /// @param isGrant Whether this is a grant (true) or revoke (false) operation
    struct RoleProposalParams {
        ProposalType proposalType;
        address targetAddress;
        bytes32 role;
        bool isGrant;
    }

    /// @notice Thrown when an invalid address is provided
    error InvalidAddress();

    /// @notice Thrown when an invalid role is provided
    error InvalidRole();

    /// @notice Thrown when proposal type doesn't match the role
    error ProposalTypeMismatch();

    /// @notice Creates actions for appointing a Manager
    /// @param _dao The DAO address
    /// @param _manager The address to be appointed as Manager
    /// @return actions Array of actions to execute
    function createManagerAppointmentActions(
        address _dao,
        address _manager
    ) external pure returns (IDAO.Action[] memory actions) {
        if (_manager == address(0)) {
            revert InvalidAddress();
        }

        actions = new IDAO.Action[](1);
        
        // Grant MANAGER_ROLE to the specified address
        actions[0] = IDAO.Action({
            to: _dao,
            value: 0,
            data: abi.encodeWithSelector(
                IDAO.grant.selector,
                _dao, // where: the DAO contract
                _manager, // who: the manager address
                MANAGER_ROLE // permissionId: MANAGER_ROLE
            )
        });
    }

    /// @notice Creates actions for appointing Council Members
    /// @param _dao The DAO address
    /// @param _councilMembers Array of addresses to be appointed as Council Members
    /// @return actions Array of actions to execute
    function createCouncilAppointmentActions(
        address _dao,
        address[] calldata _councilMembers
    ) external pure returns (IDAO.Action[] memory actions) {
        if (_councilMembers.length == 0) {
            revert InvalidAddress();
        }

        actions = new IDAO.Action[](_councilMembers.length);
        
        for (uint256 i = 0; i < _councilMembers.length; i++) {
            if (_councilMembers[i] == address(0)) {
                revert InvalidAddress();
            }

            // Grant COUNCIL_ROLE to each specified address
            actions[i] = IDAO.Action({
                to: _dao,
                value: 0,
                data: abi.encodeWithSelector(
                    IDAO.grant.selector,
                    _dao, // where: the DAO contract
                    _councilMembers[i], // who: the council member address
                    COUNCIL_ROLE // permissionId: COUNCIL_ROLE
                )
            });
        }
    }

    /// @notice Creates actions for removing a Manager
    /// @param _dao The DAO address
    /// @param _manager The address to be removed from Manager role
    /// @return actions Array of actions to execute
    function createManagerRemovalActions(
        address _dao,
        address _manager
    ) external pure returns (IDAO.Action[] memory actions) {
        if (_manager == address(0)) {
            revert InvalidAddress();
        }

        actions = new IDAO.Action[](1);
        
        // Revoke MANAGER_ROLE from the specified address
        actions[0] = IDAO.Action({
            to: _dao,
            value: 0,
            data: abi.encodeWithSelector(
                IDAO.revoke.selector,
                _dao, // where: the DAO contract
                _manager, // who: the manager address
                MANAGER_ROLE // permissionId: MANAGER_ROLE
            )
        });
    }

    /// @notice Creates actions for removing Council Members
    /// @param _dao The DAO address
    /// @param _councilMembers Array of addresses to be removed from Council Member role
    /// @return actions Array of actions to execute
    function createCouncilRemovalActions(
        address _dao,
        address[] calldata _councilMembers
    ) external pure returns (IDAO.Action[] memory actions) {
        if (_councilMembers.length == 0) {
            revert InvalidAddress();
        }

        actions = new IDAO.Action[](_councilMembers.length);
        
        for (uint256 i = 0; i < _councilMembers.length; i++) {
            if (_councilMembers[i] == address(0)) {
                revert InvalidAddress();
            }

            // Revoke COUNCIL_ROLE from each specified address
            actions[i] = IDAO.Action({
                to: _dao,
                value: 0,
                data: abi.encodeWithSelector(
                    IDAO.revoke.selector,
                    _dao, // where: the DAO contract
                    _councilMembers[i], // who: the council member address
                    COUNCIL_ROLE // permissionId: COUNCIL_ROLE
                )
            });
        }
    }

    /// @notice Creates a standardized proposal metadata for role appointments
    /// @param _proposalType The type of proposal
    /// @param _targetAddresses Array of target addresses
    /// @param _description Additional description for the proposal
    /// @return metadata Encoded metadata for the proposal
    function createRoleProposalMetadata(
        ProposalType _proposalType,
        address[] calldata _targetAddresses,
        string calldata _description
    ) external pure returns (bytes memory metadata) {
        string memory title;
        string memory summary;

        if (_proposalType == ProposalType.ManagerAppointment) {
            title = "Manager Appointment";
            summary = "Proposal to appoint a new Manager";
        } else if (_proposalType == ProposalType.CouncilAppointment) {
            title = "Council Member Appointment";
            summary = "Proposal to appoint new Council Members";
        } else if (_proposalType == ProposalType.ManagerRemoval) {
            title = "Manager Removal";
            summary = "Proposal to remove a Manager";
        } else if (_proposalType == ProposalType.CouncilRemoval) {
            title = "Council Member Removal";
            summary = "Proposal to remove Council Members";
        } else {
            title = "Custom Role Proposal";
            summary = "Custom role management proposal";
        }

        // Create structured metadata
        metadata = abi.encode(
            title,
            summary,
            _description,
            _targetAddresses,
            _proposalType
        );
    }

    /// @notice Validates role proposal parameters
    /// @param _params The role proposal parameters
    /// @return isValid Whether the parameters are valid
    function validateRoleProposal(RoleProposalParams calldata _params) external pure returns (bool isValid) {
        // Check if target address is valid
        if (_params.targetAddress == address(0)) {
            return false;
        }

        // Check if proposal type matches the role
        if (_params.proposalType == ProposalType.ManagerAppointment || 
            _params.proposalType == ProposalType.ManagerRemoval) {
            if (_params.role != MANAGER_ROLE) {
                return false;
            }
        } else if (_params.proposalType == ProposalType.CouncilAppointment || 
                   _params.proposalType == ProposalType.CouncilRemoval) {
            if (_params.role != COUNCIL_ROLE) {
                return false;
            }
        }

        // Check grant/revoke consistency
        if (_params.proposalType == ProposalType.ManagerAppointment || 
            _params.proposalType == ProposalType.CouncilAppointment) {
            if (!_params.isGrant) {
                return false;
            }
        } else if (_params.proposalType == ProposalType.ManagerRemoval || 
                   _params.proposalType == ProposalType.CouncilRemoval) {
            if (_params.isGrant) {
                return false;
            }
        }

        return true;
    }

    /// @notice Gets the role identifier for a proposal type
    /// @param _proposalType The proposal type
    /// @return role The corresponding role identifier
    function getRoleForProposalType(ProposalType _proposalType) external pure returns (bytes32 role) {
        if (_proposalType == ProposalType.ManagerAppointment || 
            _proposalType == ProposalType.ManagerRemoval) {
            return MANAGER_ROLE;
        } else if (_proposalType == ProposalType.CouncilAppointment || 
                   _proposalType == ProposalType.CouncilRemoval) {
            return COUNCIL_ROLE;
        } else {
            revert InvalidRole();
        }
    }
}
