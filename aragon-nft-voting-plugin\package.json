{"name": "@aragon/nft-voting-plugin", "description": "Aragon OSx plugin for NFT-based voting using ERC-721 tokens", "version": "1.0.0", "license": "AGPL-3.0-or-later", "author": {"name": "Aragon", "url": "https://github.com/aragon"}, "private": true, "scripts": {"build": "cross-env TS_NODE_TRANSPILE_ONLY=true hardhat compile", "coverage": "hardhat coverage --solcoverjs ./.solcover.js --temp artifacts --testfiles \"test/**/*.ts\" && yarn typechain", "deploy": "hardhat deploy", "lint": "yarn lint:sol && yarn lint:ts", "lint:sol": "solhint --max-warnings 0 \"contracts/**/*.sol\"", "lint:ts": "eslint --ext .js,.ts ./", "test": "hardhat test", "typechain": "cross-env TS_NODE_TRANSPILE_ONLY=true hardhat typechain", "clean": "rimraf ./artifacts ./cache ./coverage ./typechain ./types ./coverage.json", "prettier:check": "prettier --check \"**/*.{js,json,md,sol,ts,yml}\"", "prettier:write": "prettier --write \"**/*.{js,json,md,sol,ts,yml}\""}, "dependencies": {"@aragon/osx-commons-contracts": "^1.4.0", "@openzeppelin/contracts": "^4.9.6", "@openzeppelin/contracts-upgradeable": "^4.9.6"}, "devDependencies": {"@aragon/osx": "^1.4.0", "@aragon/osx-commons-configs": "^0.8.0", "@aragon/osx-commons-sdk": "^0.2.0", "@aragon/osx-ethers": "^1.4.0", "@ethersproject/abi": "5.7.0", "@ethersproject/abstract-signer": "5.7.0", "@ethersproject/bignumber": "5.7.0", "@ethersproject/bytes": "5.7.0", "@ethersproject/providers": "5.7.2", "@nomicfoundation/hardhat-chai-matchers": "^1.0.5", "@nomicfoundation/hardhat-network-helpers": "^1.0.8", "@nomicfoundation/hardhat-verify": "^1.0.4", "@nomiclabs/hardhat-ethers": "^2.2.1", "@nomiclabs/hardhat-etherscan": "^3.1.8", "@openzeppelin/hardhat-upgrades": "^1.28.0", "@typechain/ethers-v5": "^10.1.1", "@typechain/hardhat": "^6.1.4", "@types/chai": "^4.3.4", "@types/mocha": "^10.0.0", "@types/node": "^18.11.9", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "chai": "^4.3.7", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "ethers": "^5.7.2", "hardhat": "^2.22.15", "hardhat-deploy": "0.12.4", "hardhat-gas-reporter": "^1.0.9", "prettier": "^2.8.8", "prettier-plugin-solidity": "^1.1.3", "rimraf": "^5.0.5", "solhint": "^3.6.2", "solhint-plugin-prettier": "^0.0.5", "solidity-coverage": "^0.8.2", "ts-node": "^10.9.1", "typechain": "^8.3.2", "typescript": "^5.2.2"}}