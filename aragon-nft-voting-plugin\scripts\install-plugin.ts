import {ethers} from 'hardhat';
import {
  NftVotingPluginSetup,
  IDAO,
  MockNFT,
} from '../typechain';
import {DEFAULT_VOTING_SETTINGS} from '../plugin-settings';

interface InstallationConfig {
  daoAddress: string;
  nftContractAddress: string;
  pluginSetupAddress: string;
  votingSettings?: {
    votingMode: number;
    supportThreshold: number;
    minParticipation: number;
    minDuration: number;
    minProposerVotingPower: number;
  };
}

async function main() {
  console.log('🔧 Starting NFT Voting Plugin installation...\n');

  const [deployer] = await ethers.getSigners();
  console.log('Installing with account:', deployer.address);
  console.log('Account balance:', ethers.utils.formatEther(await deployer.getBalance()), 'ETH\n');

  // Configuration - these should be provided via environment variables or command line args
  const config: InstallationConfig = {
    daoAddress: process.env.DAO_ADDRESS || '',
    nftContractAddress: process.env.NFT_CONTRACT_ADDRESS || '',
    pluginSetupAddress: process.env.PLUGIN_SETUP_ADDRESS || '',
    votingSettings: {
      votingMode: parseInt(process.env.VOTING_MODE || '0'),
      supportThreshold: parseInt(process.env.SUPPORT_THRESHOLD || '500000'),
      minParticipation: parseInt(process.env.MIN_PARTICIPATION || '250000'),
      minDuration: parseInt(process.env.MIN_DURATION || '86400'),
      minProposerVotingPower: parseInt(process.env.MIN_PROPOSER_VOTING_POWER || '1'),
    },
  };

  // Validate configuration
  if (!config.daoAddress) {
    throw new Error('DAO_ADDRESS environment variable is required');
  }
  if (!config.nftContractAddress) {
    throw new Error('NFT_CONTRACT_ADDRESS environment variable is required');
  }
  if (!config.pluginSetupAddress) {
    throw new Error('PLUGIN_SETUP_ADDRESS environment variable is required');
  }

  console.log('📋 Installation Configuration:');
  console.log('='.repeat(50));
  console.log(`DAO Address:           ${config.daoAddress}`);
  console.log(`NFT Contract:          ${config.nftContractAddress}`);
  console.log(`Plugin Setup:          ${config.pluginSetupAddress}`);
  console.log(`Voting Mode:           ${config.votingSettings?.votingMode}`);
  console.log(`Support Threshold:     ${config.votingSettings?.supportThreshold / 10000}%`);
  console.log(`Min Participation:     ${config.votingSettings?.minParticipation / 10000}%`);
  console.log(`Min Duration:          ${config.votingSettings?.minDuration} seconds`);
  console.log(`Min Proposer Power:    ${config.votingSettings?.minProposerVotingPower} NFTs`);
  console.log('='.repeat(50));

  // Verify contracts exist
  console.log('\n🔍 Verifying contracts...');
  
  // Check DAO contract
  const daoCode = await ethers.provider.getCode(config.daoAddress);
  if (daoCode === '0x') {
    throw new Error(`No contract found at DAO address: ${config.daoAddress}`);
  }
  console.log('✅ DAO contract verified');

  // Check NFT contract
  const nftCode = await ethers.provider.getCode(config.nftContractAddress);
  if (nftCode === '0x') {
    throw new Error(`No contract found at NFT address: ${config.nftContractAddress}`);
  }
  
  // Verify NFT contract supports ERC721
  const nftContract = await ethers.getContractAt('IERC721', config.nftContractAddress);
  try {
    await nftContract.supportsInterface('0x80ac58cd'); // ERC721 interface ID
    console.log('✅ NFT contract verified (ERC721)');
  } catch (error) {
    console.warn('⚠️  Warning: Could not verify ERC721 interface');
  }

  // Check Plugin Setup contract
  const setupCode = await ethers.provider.getCode(config.pluginSetupAddress);
  if (setupCode === '0x') {
    throw new Error(`No contract found at Plugin Setup address: ${config.pluginSetupAddress}`);
  }
  console.log('✅ Plugin Setup contract verified');

  // Get contracts
  const dao = await ethers.getContractAt('IDAO', config.daoAddress);
  const pluginSetup = await ethers.getContractAt('NftVotingPluginSetup', config.pluginSetupAddress);

  // Encode installation parameters
  console.log('\n📦 Preparing installation parameters...');
  const installationParams = await pluginSetup.encodeInstallationParams(
    config.nftContractAddress,
    config.votingSettings || DEFAULT_VOTING_SETTINGS
  );
  console.log('✅ Installation parameters encoded');

  // Prepare installation
  console.log('\n🔧 Preparing plugin installation...');
  const preparationResult = await pluginSetup.prepareInstallation(
    config.daoAddress,
    installationParams
  );

  console.log('✅ Plugin installation prepared');
  console.log('Plugin address:', preparationResult.plugin);
  console.log('Permissions to grant:', preparationResult.preparedSetupData.permissions.length);

  // Display permissions that will be granted
  console.log('\n🔐 Permissions to be granted:');
  for (let i = 0; i < preparationResult.preparedSetupData.permissions.length; i++) {
    const permission = preparationResult.preparedSetupData.permissions[i];
    console.log(`${i + 1}. ${permission.operation === 0 ? 'GRANT' : 'REVOKE'} ${permission.permissionId}`);
    console.log(`   Where: ${permission.where}`);
    console.log(`   Who:   ${permission.who}`);
  }

  // Note: In a real scenario, you would need to execute these permissions through the DAO
  // This typically requires a proposal and voting process, or admin privileges
  console.log('\n⚠️  Important Notes:');
  console.log('1. The permissions listed above need to be granted through the DAO');
  console.log('2. This typically requires creating a proposal and voting on it');
  console.log('3. Or having admin privileges to grant permissions directly');
  console.log('4. The plugin is deployed but not yet functional until permissions are granted');

  // Save installation information
  const installationInfo = {
    daoAddress: config.daoAddress,
    nftContractAddress: config.nftContractAddress,
    pluginSetupAddress: config.pluginSetupAddress,
    pluginAddress: preparationResult.plugin,
    votingSettings: config.votingSettings,
    permissions: preparationResult.preparedSetupData.permissions,
    timestamp: new Date().toISOString(),
    installer: deployer.address,
  };

  const fs = require('fs');
  const path = require('path');
  
  const installationsDir = path.join(__dirname, '..', 'installations');
  if (!fs.existsSync(installationsDir)) {
    fs.mkdirSync(installationsDir, {recursive: true});
  }

  const installationFile = path.join(
    installationsDir,
    `installation-${Date.now()}.json`
  );
  
  fs.writeFileSync(installationFile, JSON.stringify(installationInfo, null, 2));
  console.log(`\n💾 Installation info saved to: ${installationFile}`);

  console.log('\n🎯 Next Steps:');
  console.log('1. Grant the required permissions through the DAO governance process');
  console.log('2. Test the plugin functionality with the NFT contract');
  console.log('3. Create test proposals and verify voting works correctly');
  console.log('4. Set up monitoring and alerts for the plugin');

  return {
    pluginAddress: preparationResult.plugin,
    permissions: preparationResult.preparedSetupData.permissions,
    installationInfo,
  };
}

// Execute installation if this script is run directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Installation failed:', error);
      process.exit(1);
    });
}

export default main;
