import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Extend Window interface for ethereum
declare global {
  interface Window {
    ethereum?: any;
  }
}

// Contract addresses on Polygon Amoy Testnet
const CONTRACTS = {
  NFT_ADDRESS: '******************************************',
  VOTING_ADDRESS: '******************************************',
  NETWORK: {
    chainId: '0x13882', // 80002 in hex (Polygon Amoy)
    chainName: 'Polygon Amoy Testnet',
    rpcUrls: ['https://polygon-amoy.infura.io/v3/********************************'],
    blockExplorerUrls: ['https://amoy.polygonscan.com/'],
    nativeCurrency: {
      name: '<PERSON><PERSON><PERSON>',
      symbol: 'MATIC',
      decimals: 18
    }
  }
};

// Contract ABIs (simplified)
const NFT_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function ownerOf(uint256 tokenId) view returns (address)",
  "function totalSupply() view returns (uint256)",
  "function name() view returns (string)",
  "function symbol() view returns (string)"
];

const VOTING_ABI = [
  "function createProposal(string description) returns (uint256)",
  "function vote(uint256 proposalId, uint256 tokenId, uint8 voteOption)",
  "function getProposal(uint256 proposalId) view returns (string, uint256, uint256, uint256, uint256, uint256, bool)",
  "function hasProposalPassed(uint256 proposalId) view returns (bool)",
  "function proposalCounter() view returns (uint256)",
  "function hasTokenVoted(uint256 proposalId, uint256 tokenId) view returns (bool)",
  "function getTotalVotingPower() view returns (uint256)",
  "event ProposalCreated(uint256 indexed proposalId, string description, uint256 startTime, uint256 endTime)",
  "event VoteCast(uint256 indexed proposalId, address indexed voter, uint256 indexed tokenId, uint8 vote)"
];

interface Proposal {
  id: number;
  description: string;
  startTime: number;
  endTime: number;
  yesVotes: number;
  noVotes: number;
  abstainVotes: number;
  executed: boolean;
  hasPassed?: boolean;
}

interface UserNFT {
  tokenId: number;
  canVote: boolean;
}

const DAOVoting = () => {
  const [provider, setProvider] = useState<ethers.providers.Web3Provider | null>(null);
  const [signer, setSigner] = useState<ethers.Signer | null>(null);
  const [account, setAccount] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [userNFTs, setUserNFTs] = useState<UserNFT[]>([]);
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(false);
  const [newProposal, setNewProposal] = useState('');
  const [isOwner, setIsOwner] = useState(false);

  // Connect to wallet
  const connectWallet = async () => {
    try {
      if (!window.ethereum) {
        alert('Please install MetaMask!');
        return;
      }

      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const accounts = await provider.send('eth_requestAccounts', []);
      
      // Switch to Polygon Amoy if needed
      try {
        await provider.send('wallet_switchEthereumChain', [{ chainId: CONTRACTS.NETWORK.chainId }]);
      } catch (error: any) {
        if (error.code === 4902) {
          await provider.send('wallet_addEthereumChain', [CONTRACTS.NETWORK]);
        }
      }

      const signer = provider.getSigner();
      const account = accounts[0];

      setProvider(provider);
      setSigner(signer);
      setAccount(account);
      setIsConnected(true);

      // Check if user is contract owner
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);
      const owner = await votingContract.owner();
      setIsOwner(account.toLowerCase() === owner.toLowerCase());

      await loadUserData(signer, account);
    } catch (error) {
      console.error('Error connecting wallet:', error);
      alert('Failed to connect wallet');
    }
  };

  // Load user's NFTs and voting data
  const loadUserData = async (signer: ethers.Signer, account: string) => {
    try {
      setLoading(true);
      
      const nftContract = new ethers.Contract(CONTRACTS.NFT_ADDRESS, NFT_ABI, signer);
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);

      // Get user's NFT balance
      const balance = await nftContract.balanceOf(account);
      const totalSupply = await nftContract.totalSupply();

      // Find user's NFTs
      const userTokens: UserNFT[] = [];
      for (let i = 1; i <= totalSupply.toNumber(); i++) {
        try {
          const owner = await nftContract.ownerOf(i);
          if (owner.toLowerCase() === account.toLowerCase()) {
            userTokens.push({
              tokenId: i,
              canVote: true
            });
          }
        } catch (error) {
          // Token doesn't exist or error
        }
      }

      setUserNFTs(userTokens);
      await loadProposals(votingContract);
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load all proposals
  const loadProposals = async (votingContract: ethers.Contract) => {
    try {
      const proposalCount = await votingContract.proposalCounter();
      const proposalList: Proposal[] = [];

      for (let i = 1; i <= proposalCount.toNumber(); i++) {
        const proposal = await votingContract.getProposal(i);
        const hasPassed = await votingContract.hasProposalPassed(i);

        proposalList.push({
          id: i,
          description: proposal[0],
          startTime: proposal[1].toNumber(),
          endTime: proposal[2].toNumber(),
          yesVotes: proposal[3].toNumber(),
          noVotes: proposal[4].toNumber(),
          abstainVotes: proposal[5].toNumber(),
          executed: proposal[6],
          hasPassed
        });
      }

      setProposals(proposalList.reverse()); // Show newest first
    } catch (error) {
      console.error('Error loading proposals:', error);
    }
  };

  // Create new proposal
  const createProposal = async () => {
    if (!signer || !newProposal.trim()) return;

    try {
      setLoading(true);
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);
      
      const tx = await votingContract.createProposal(newProposal);
      await tx.wait();
      
      setNewProposal('');
      await loadUserData(signer, account);
      alert('Proposal created successfully!');
    } catch (error) {
      console.error('Error creating proposal:', error);
      alert('Failed to create proposal');
    } finally {
      setLoading(false);
    }
  };

  // Vote on proposal
  const vote = async (proposalId: number, tokenId: number, voteOption: number) => {
    if (!signer) return;

    try {
      setLoading(true);
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);
      
      const tx = await votingContract.vote(proposalId, tokenId, voteOption);
      await tx.wait();
      
      await loadUserData(signer, account);
      alert('Vote cast successfully!');
    } catch (error) {
      console.error('Error voting:', error);
      alert('Failed to cast vote. You may have already voted with this NFT.');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Check if voting is active
  const isVotingActive = (proposal: Proposal) => {
    const now = Date.now() / 1000;
    return now >= proposal.startTime && now <= proposal.endTime;
  };

  if (loading && !isConnected) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-2xl p-12 shadow-xl border border-gray-100 text-center">
          <div className="animate-spin w-12 h-12 border-4 border-purple-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading DAO interface...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto animate-fade-in">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 rounded-2xl p-8 mb-8 text-white shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
              WE3Home DAO Voting
            </h1>
            <p className="text-purple-100 text-lg">Decentralized governance powered by your WE3Home NFTs</p>
          </div>
          <div className="hidden md:block">
            <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 border border-white/30">
              <div className="text-2xl font-bold">🗳️</div>
              <div className="text-sm opacity-90">Democracy</div>
            </div>
          </div>
        </div>

        {/* Stats Row */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="text-2xl font-bold">{userNFTs.length}</div>
            <div className="text-sm opacity-90">Your Voting Power</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="text-2xl font-bold">{proposals.length}</div>
            <div className="text-sm opacity-90">Active Proposals</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="text-2xl font-bold">5,200</div>
            <div className="text-sm opacity-90">Max DAO Members</div>
          </div>
        </div>
      </div>

      {/* Connection Status */}
      <div className="mb-8">
        {!isConnected ? (
          <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Connect Your Wallet</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                Connect your wallet to participate in DAO governance and vote with your WE3Home NFTs
              </p>
              <button
                onClick={connectWallet}
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold"
              >
                🦊 Connect MetaMask
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <p className="text-green-700 font-semibold text-lg">✅ Wallet Connected</p>
                  <p className="text-gray-600">{account.slice(0, 6)}...{account.slice(-4)}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{userNFTs.length}</div>
                    <div className="text-sm text-gray-500">NFTs Owned</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{userNFTs.length}</div>
                    <div className="text-sm text-gray-500">Voting Power</div>
                  </div>
                </div>
              </div>
            </div>
            {isOwner && (
              <div className="mt-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
                <p className="text-purple-700 font-semibold flex items-center">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                  Contract Owner - You can create proposals
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Create Proposal (Owner Only) */}
      {isConnected && isOwner && (
        <div className="mb-8">
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6 border border-purple-200 shadow-lg">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-800">Create New Proposal</h2>
            </div>
            <p className="text-gray-600 mb-6">As the contract owner, you can create governance proposals for the DAO to vote on.</p>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Proposal Description</label>
                <textarea
                  value={newProposal}
                  onChange={(e) => setNewProposal(e.target.value)}
                  placeholder="Describe your proposal in detail... (e.g., 'Should we implement a new feature for the WE3Home platform?')"
                  rows={3}
                  className="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200"
                />
              </div>
              <div className="flex justify-end">
                <button
                  onClick={createProposal}
                  disabled={loading || !newProposal.trim()}
                  className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-3 rounded-xl hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold flex items-center"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating...
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Create Proposal
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Proposals List */}
      {isConnected && (
        <div>
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-800 flex items-center">
              <svg className="w-8 h-8 mr-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              DAO Proposals
            </h2>
            <div className="text-right">
              <div className="text-sm text-gray-500">Total Proposals</div>
              <div className="text-2xl font-bold text-purple-600">{proposals.length}</div>
            </div>
          </div>

          {proposals.length === 0 ? (
            <div className="bg-white rounded-2xl p-12 text-center shadow-xl border border-gray-100">
              <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No Proposals Yet</h3>
              <p className="text-gray-600 mb-6">
                {isOwner ? 'Create the first proposal to get DAO governance started!' : 'Check back later for new proposals to vote on.'}
              </p>
              {isOwner && (
                <p className="text-sm text-purple-600 font-medium">Use the form above to create your first proposal</p>
              )}
            </div>
          ) : (
            <div className="space-y-8">
              {proposals.map((proposal) => (
                <div key={proposal.id} className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300">
                  {/* Proposal Header */}
                  <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                          <span className="text-white font-bold text-lg">#{proposal.id}</span>
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-gray-800">Proposal #{proposal.id}</h3>
                          <p className="text-sm text-gray-500">Created {formatDate(proposal.startTime)}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {isVotingActive(proposal) && (
                          <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                            Active
                          </span>
                        )}
                        {proposal.hasPassed && (
                          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Passed
                          </span>
                        )}
                        {proposal.executed && (
                          <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-semibold">Executed</span>
                        )}
                        {!isVotingActive(proposal) && !proposal.hasPassed && (
                          <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold">Failed</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Proposal Content */}
                  <div className="p-6">
                    {/* Description */}
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-800 mb-3">Description</h4>
                      <p className="text-gray-700 leading-relaxed bg-gray-50 p-4 rounded-xl">{proposal.description}</p>
                    </div>

                    {/* Voting Results */}
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4">Voting Results</h4>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="bg-green-50 rounded-xl p-4 text-center border border-green-200">
                          <div className="text-3xl font-bold text-green-600 mb-1">{proposal.yesVotes}</div>
                          <div className="text-sm font-medium text-green-700">Yes Votes</div>
                          <div className="w-full bg-green-200 rounded-full h-2 mt-2">
                            <div
                              className="bg-green-500 h-2 rounded-full transition-all duration-500"
                              style={{
                                width: `${proposal.yesVotes + proposal.noVotes + proposal.abstainVotes > 0
                                  ? (proposal.yesVotes / (proposal.yesVotes + proposal.noVotes + proposal.abstainVotes)) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="bg-red-50 rounded-xl p-4 text-center border border-red-200">
                          <div className="text-3xl font-bold text-red-600 mb-1">{proposal.noVotes}</div>
                          <div className="text-sm font-medium text-red-700">No Votes</div>
                          <div className="w-full bg-red-200 rounded-full h-2 mt-2">
                            <div
                              className="bg-red-500 h-2 rounded-full transition-all duration-500"
                              style={{
                                width: `${proposal.yesVotes + proposal.noVotes + proposal.abstainVotes > 0
                                  ? (proposal.noVotes / (proposal.yesVotes + proposal.noVotes + proposal.abstainVotes)) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>
                        <div className="bg-gray-50 rounded-xl p-4 text-center border border-gray-200">
                          <div className="text-3xl font-bold text-gray-600 mb-1">{proposal.abstainVotes}</div>
                          <div className="text-sm font-medium text-gray-700">Abstain</div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div
                              className="bg-gray-500 h-2 rounded-full transition-all duration-500"
                              style={{
                                width: `${proposal.yesVotes + proposal.noVotes + proposal.abstainVotes > 0
                                  ? (proposal.abstainVotes / (proposal.yesVotes + proposal.noVotes + proposal.abstainVotes)) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Timing Info */}
                    <div className="mb-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center text-blue-700">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="font-medium">
                            {isVotingActive(proposal) ? 'Voting ends:' : 'Voting ended:'}
                          </span>
                        </div>
                        <span className="font-semibold text-blue-800">{formatDate(proposal.endTime)}</span>
                      </div>
                    </div>
                  
                    {/* Voting Section */}
                    {isVotingActive(proposal) && userNFTs.length > 0 && (
                      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6 border border-purple-200">
                        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                          <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                          </svg>
                          Cast Your Vote
                        </h4>
                        <p className="text-gray-600 mb-4">Use your WE3Home NFTs to vote on this proposal:</p>
                        <div className="space-y-3">
                          {userNFTs.map((nft) => (
                            <div key={nft.tokenId} className="bg-white rounded-lg p-4 border border-gray-200">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mr-3">
                                    <span className="text-white font-bold text-sm">#{nft.tokenId}</span>
                                  </div>
                                  <div>
                                    <div className="font-semibold text-gray-800">WE3Home NFT #{nft.tokenId}</div>
                                    <div className="text-sm text-gray-500">1 Voting Power</div>
                                  </div>
                                </div>
                                <div className="flex gap-2">
                                  <button
                                    onClick={() => vote(proposal.id, nft.tokenId, 2)}
                                    disabled={loading}
                                    className="bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 flex items-center"
                                  >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                    Yes
                                  </button>
                                  <button
                                    onClick={() => vote(proposal.id, nft.tokenId, 3)}
                                    disabled={loading}
                                    className="bg-red-500 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 flex items-center"
                                  >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    No
                                  </button>
                                  <button
                                    onClick={() => vote(proposal.id, nft.tokenId, 1)}
                                    disabled={loading}
                                    className="bg-gray-500 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 flex items-center"
                                  >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                    Abstain
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {userNFTs.length === 0 && isVotingActive(proposal) && (
                      <div className="bg-yellow-50 rounded-xl p-6 border border-yellow-200">
                        <div className="flex items-center">
                          <svg className="w-6 h-6 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                          </svg>
                          <div>
                            <h4 className="font-semibold text-yellow-800">NFT Required to Vote</h4>
                            <p className="text-yellow-700">You need to own a WE3Home NFT to participate in this proposal.</p>
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Contract Info */}
      <div className="mt-12">
        <div className="bg-gradient-to-r from-gray-900 to-blue-900 rounded-2xl p-8 text-white shadow-2xl">
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold">Smart Contract Information</h3>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold text-lg mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                WE3Home NFT Contract
              </h4>
              <div className="space-y-3">
                <div>
                  <div className="text-sm opacity-75">Contract Address</div>
                  <a
                    href={`https://amoy.polygonscan.com/address/${CONTRACTS.NFT_ADDRESS}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-300 hover:text-blue-200 transition-colors font-mono text-sm break-all hover:underline"
                  >
                    {CONTRACTS.NFT_ADDRESS}
                  </a>
                </div>
                <div>
                  <div className="text-sm opacity-75">Purpose</div>
                  <div className="text-sm">NFT membership tokens (1 NFT = 1 vote)</div>
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <h4 className="font-semibold text-lg mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
                DAO Voting Contract
              </h4>
              <div className="space-y-3">
                <div>
                  <div className="text-sm opacity-75">Contract Address</div>
                  <a
                    href={`https://amoy.polygonscan.com/address/${CONTRACTS.VOTING_ADDRESS}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-300 hover:text-blue-200 transition-colors font-mono text-sm break-all hover:underline"
                  >
                    {CONTRACTS.VOTING_ADDRESS}
                  </a>
                </div>
                <div>
                  <div className="text-sm opacity-75">Purpose</div>
                  <div className="text-sm">Governance voting and proposal management</div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-white/5 rounded-xl border border-white/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium">Network: Polygon Amoy Testnet</span>
              </div>
              <div className="text-sm opacity-75">Safe testing environment</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DAOVoting;
