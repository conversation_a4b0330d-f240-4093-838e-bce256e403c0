import React, { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// Contract addresses on Polygon Amoy Testnet
const CONTRACTS = {
  NFT_ADDRESS: '******************************************',
  VOTING_ADDRESS: '******************************************',
  NETWORK: {
    chainId: '0x13882', // 80002 in hex (Polygon Amoy)
    chainName: 'Polygon Amoy Testnet',
    rpcUrls: ['https://polygon-amoy.infura.io/v3/********************************'],
    blockExplorerUrls: ['https://amoy.polygonscan.com/'],
    nativeCurrency: {
      name: 'MATIC',
      symbol: 'MATIC',
      decimals: 18
    }
  }
};

// Contract ABIs (simplified)
const NFT_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function ownerOf(uint256 tokenId) view returns (address)",
  "function totalSupply() view returns (uint256)",
  "function name() view returns (string)",
  "function symbol() view returns (string)"
];

const VOTING_ABI = [
  "function createProposal(string description) returns (uint256)",
  "function vote(uint256 proposalId, uint256 tokenId, uint8 voteOption)",
  "function getProposal(uint256 proposalId) view returns (string, uint256, uint256, uint256, uint256, uint256, bool)",
  "function hasProposalPassed(uint256 proposalId) view returns (bool)",
  "function proposalCounter() view returns (uint256)",
  "function hasTokenVoted(uint256 proposalId, uint256 tokenId) view returns (bool)",
  "function getTotalVotingPower() view returns (uint256)",
  "event ProposalCreated(uint256 indexed proposalId, string description, uint256 startTime, uint256 endTime)",
  "event VoteCast(uint256 indexed proposalId, address indexed voter, uint256 indexed tokenId, uint8 vote)"
];

interface Proposal {
  id: number;
  description: string;
  startTime: number;
  endTime: number;
  yesVotes: number;
  noVotes: number;
  abstainVotes: number;
  executed: boolean;
  hasPassed?: boolean;
}

interface UserNFT {
  tokenId: number;
  canVote: boolean;
}

const DAOVoting: React.FC = () => {
  const [provider, setProvider] = useState<ethers.providers.Web3Provider | null>(null);
  const [signer, setSigner] = useState<ethers.Signer | null>(null);
  const [account, setAccount] = useState<string>('');
  const [isConnected, setIsConnected] = useState(false);
  const [userNFTs, setUserNFTs] = useState<UserNFT[]>([]);
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(false);
  const [newProposal, setNewProposal] = useState('');
  const [isOwner, setIsOwner] = useState(false);

  // Connect to wallet
  const connectWallet = async () => {
    try {
      if (!window.ethereum) {
        alert('Please install MetaMask!');
        return;
      }

      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const accounts = await provider.send('eth_requestAccounts', []);
      
      // Switch to Polygon Amoy if needed
      try {
        await provider.send('wallet_switchEthereumChain', [{ chainId: CONTRACTS.NETWORK.chainId }]);
      } catch (error: any) {
        if (error.code === 4902) {
          await provider.send('wallet_addEthereumChain', [CONTRACTS.NETWORK]);
        }
      }

      const signer = provider.getSigner();
      const account = accounts[0];

      setProvider(provider);
      setSigner(signer);
      setAccount(account);
      setIsConnected(true);

      // Check if user is contract owner
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);
      const owner = await votingContract.owner();
      setIsOwner(account.toLowerCase() === owner.toLowerCase());

      await loadUserData(signer, account);
    } catch (error) {
      console.error('Error connecting wallet:', error);
      alert('Failed to connect wallet');
    }
  };

  // Load user's NFTs and voting data
  const loadUserData = async (signer: ethers.Signer, account: string) => {
    try {
      setLoading(true);
      
      const nftContract = new ethers.Contract(CONTRACTS.NFT_ADDRESS, NFT_ABI, signer);
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);

      // Get user's NFT balance
      const balance = await nftContract.balanceOf(account);
      const totalSupply = await nftContract.totalSupply();

      // Find user's NFTs
      const userTokens: UserNFT[] = [];
      for (let i = 1; i <= totalSupply.toNumber(); i++) {
        try {
          const owner = await nftContract.ownerOf(i);
          if (owner.toLowerCase() === account.toLowerCase()) {
            userTokens.push({
              tokenId: i,
              canVote: true
            });
          }
        } catch (error) {
          // Token doesn't exist or error
        }
      }

      setUserNFTs(userTokens);
      await loadProposals(votingContract);
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load all proposals
  const loadProposals = async (votingContract: ethers.Contract) => {
    try {
      const proposalCount = await votingContract.proposalCounter();
      const proposalList: Proposal[] = [];

      for (let i = 1; i <= proposalCount.toNumber(); i++) {
        const proposal = await votingContract.getProposal(i);
        const hasPassed = await votingContract.hasProposalPassed(i);

        proposalList.push({
          id: i,
          description: proposal[0],
          startTime: proposal[1].toNumber(),
          endTime: proposal[2].toNumber(),
          yesVotes: proposal[3].toNumber(),
          noVotes: proposal[4].toNumber(),
          abstainVotes: proposal[5].toNumber(),
          executed: proposal[6],
          hasPassed
        });
      }

      setProposals(proposalList.reverse()); // Show newest first
    } catch (error) {
      console.error('Error loading proposals:', error);
    }
  };

  // Create new proposal
  const createProposal = async () => {
    if (!signer || !newProposal.trim()) return;

    try {
      setLoading(true);
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);
      
      const tx = await votingContract.createProposal(newProposal);
      await tx.wait();
      
      setNewProposal('');
      await loadUserData(signer, account);
      alert('Proposal created successfully!');
    } catch (error) {
      console.error('Error creating proposal:', error);
      alert('Failed to create proposal');
    } finally {
      setLoading(false);
    }
  };

  // Vote on proposal
  const vote = async (proposalId: number, tokenId: number, voteOption: number) => {
    if (!signer) return;

    try {
      setLoading(true);
      const votingContract = new ethers.Contract(CONTRACTS.VOTING_ADDRESS, VOTING_ABI, signer);
      
      const tx = await votingContract.vote(proposalId, tokenId, voteOption);
      await tx.wait();
      
      await loadUserData(signer, account);
      alert('Vote cast successfully!');
    } catch (error) {
      console.error('Error voting:', error);
      alert('Failed to cast vote. You may have already voted with this NFT.');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  // Check if voting is active
  const isVotingActive = (proposal: Proposal) => {
    const now = Date.now() / 1000;
    return now >= proposal.startTime && now <= proposal.endTime;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">WE3Home DAO Voting</h1>
        <p className="text-gray-600">Decentralized governance powered by your WE3Home NFTs</p>
      </div>

      {/* Connection Status */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        {!isConnected ? (
          <div className="text-center">
            <p className="text-gray-700 mb-4">Connect your wallet to participate in DAO governance</p>
            <button
              onClick={connectWallet}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Connect Wallet
            </button>
          </div>
        ) : (
          <div>
            <p className="text-green-700 font-semibold">✅ Connected: {account.slice(0, 6)}...{account.slice(-4)}</p>
            <p className="text-gray-600">Your NFTs: {userNFTs.length} | Voting Power: {userNFTs.length}</p>
            {isOwner && <p className="text-purple-600 font-semibold">🔑 You are the contract owner</p>}
          </div>
        )}
      </div>

      {/* Create Proposal (Owner Only) */}
      {isConnected && isOwner && (
        <div className="mb-8 p-4 bg-purple-50 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Create New Proposal</h2>
          <div className="flex gap-4">
            <input
              type="text"
              value={newProposal}
              onChange={(e) => setNewProposal(e.target.value)}
              placeholder="Enter proposal description..."
              className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <button
              onClick={createProposal}
              disabled={loading || !newProposal.trim()}
              className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            >
              {loading ? 'Creating...' : 'Create Proposal'}
            </button>
          </div>
        </div>
      )}

      {/* Proposals List */}
      {isConnected && (
        <div>
          <h2 className="text-2xl font-semibold mb-6">Active Proposals</h2>
          
          {proposals.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No proposals yet. {isOwner ? 'Create the first proposal!' : 'Check back later.'}</p>
            </div>
          ) : (
            <div className="space-y-6">
              {proposals.map((proposal) => (
                <div key={proposal.id} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold">Proposal #{proposal.id}</h3>
                    <div className="flex gap-2">
                      {isVotingActive(proposal) && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Active</span>
                      )}
                      {proposal.hasPassed && (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">Passed</span>
                      )}
                      {proposal.executed && (
                        <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">Executed</span>
                      )}
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-4">{proposal.description}</p>
                  
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{proposal.yesVotes}</div>
                      <div className="text-sm text-gray-500">Yes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{proposal.noVotes}</div>
                      <div className="text-sm text-gray-500">No</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-600">{proposal.abstainVotes}</div>
                      <div className="text-sm text-gray-500">Abstain</div>
                    </div>
                  </div>
                  
                  <div className="text-sm text-gray-500 mb-4">
                    <p>Voting ends: {formatDate(proposal.endTime)}</p>
                  </div>
                  
                  {/* Voting Buttons */}
                  {isVotingActive(proposal) && userNFTs.length > 0 && (
                    <div className="border-t pt-4">
                      <p className="text-sm text-gray-600 mb-2">Vote with your NFTs:</p>
                      {userNFTs.map((nft) => (
                        <div key={nft.tokenId} className="flex items-center gap-2 mb-2">
                          <span className="text-sm">NFT #{nft.tokenId}:</span>
                          <button
                            onClick={() => vote(proposal.id, nft.tokenId, 2)}
                            disabled={loading}
                            className="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 disabled:opacity-50"
                          >
                            Yes
                          </button>
                          <button
                            onClick={() => vote(proposal.id, nft.tokenId, 3)}
                            disabled={loading}
                            className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600 disabled:opacity-50"
                          >
                            No
                          </button>
                          <button
                            onClick={() => vote(proposal.id, nft.tokenId, 1)}
                            disabled={loading}
                            className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600 disabled:opacity-50"
                          >
                            Abstain
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {userNFTs.length === 0 && (
                    <div className="border-t pt-4">
                      <p className="text-sm text-gray-500">You need a WE3Home NFT to vote on this proposal.</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Contract Info */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg text-sm text-gray-600">
        <h3 className="font-semibold mb-2">Contract Information</h3>
        <p>NFT Contract: <a href={`https://amoy.polygonscan.com/address/${CONTRACTS.NFT_ADDRESS}`} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{CONTRACTS.NFT_ADDRESS}</a></p>
        <p>Voting Contract: <a href={`https://amoy.polygonscan.com/address/${CONTRACTS.VOTING_ADDRESS}`} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{CONTRACTS.VOTING_ADDRESS}</a></p>
        <p>Network: Polygon Amoy Testnet</p>
      </div>
    </div>
  );
};

export default DAOVoting;
