// SPDX-License-Identifier: AGPL-3.0-or-later
pragma solidity 0.8.17;

import {ERC721} from "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import {ERC721Enumerable} from "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";

/// @title MockNFT
/// <AUTHOR>
/// @notice Mock NFT contract for testing the NFT Voting Plugin
/// @dev This contract is used for testing purposes only
contract MockNFT is ERC721Enumerable, Ownable {
    uint256 private _nextTokenId;
    string private _baseTokenURI;

    /// @notice Constructor
    /// @param _name The name of the NFT collection
    /// @param _symbol The symbol of the NFT collection
    /// @param _baseURI The base URI for token metadata
    constructor(
        string memory _name,
        string memory _symbol,
        string memory _baseURI
    ) ERC721(_name, _symbol) {
        _baseTokenURI = _baseURI;
        _nextTokenId = 1;
    }

    /// @notice Mints a new NFT to the specified address
    /// @param _to The address to mint the NFT to
    /// @return tokenId The ID of the newly minted token
    function mint(address _to) external onlyOwner returns (uint256 tokenId) {
        tokenId = _nextTokenId;
        _nextTokenId++;
        _safeMint(_to, tokenId);
    }

    /// @notice Mints multiple NFTs to the specified address
    /// @param _to The address to mint the NFTs to
    /// @param _amount The number of NFTs to mint
    /// @return tokenIds Array of newly minted token IDs
    function mintBatch(address _to, uint256 _amount) external onlyOwner returns (uint256[] memory tokenIds) {
        tokenIds = new uint256[](_amount);
        for (uint256 i = 0; i < _amount; i++) {
            tokenIds[i] = _nextTokenId;
            _safeMint(_to, _nextTokenId);
            _nextTokenId++;
        }
    }

    /// @notice Mints NFTs to multiple addresses
    /// @param _recipients Array of addresses to mint NFTs to
    /// @param _amounts Array of amounts to mint to each address
    function mintToMultiple(address[] calldata _recipients, uint256[] calldata _amounts) external onlyOwner {
        require(_recipients.length == _amounts.length, "Arrays length mismatch");
        
        for (uint256 i = 0; i < _recipients.length; i++) {
            for (uint256 j = 0; j < _amounts[i]; j++) {
                _safeMint(_recipients[i], _nextTokenId);
                _nextTokenId++;
            }
        }
    }

    /// @notice Burns an NFT
    /// @param _tokenId The ID of the token to burn
    function burn(uint256 _tokenId) external {
        require(_isApprovedOrOwner(_msgSender(), _tokenId), "Not approved or owner");
        _burn(_tokenId);
    }

    /// @notice Sets the base URI for token metadata
    /// @param _baseURI The new base URI
    function setBaseURI(string calldata _baseURI) external onlyOwner {
        _baseTokenURI = _baseURI;
    }

    /// @notice Returns the base URI for token metadata
    /// @return The base URI
    function _baseURI() internal view override returns (string memory) {
        return _baseTokenURI;
    }

    /// @notice Returns the next token ID that will be minted
    /// @return The next token ID
    function nextTokenId() external view returns (uint256) {
        return _nextTokenId;
    }

    /// @notice Returns all token IDs owned by an address
    /// @param _owner The owner address
    /// @return tokenIds Array of token IDs owned by the address
    function tokensOfOwner(address _owner) external view returns (uint256[] memory tokenIds) {
        uint256 balance = balanceOf(_owner);
        tokenIds = new uint256[](balance);
        
        for (uint256 i = 0; i < balance; i++) {
            tokenIds[i] = tokenOfOwnerByIndex(_owner, i);
        }
    }

    /// @notice Checks if a token exists
    /// @param _tokenId The token ID to check
    /// @return True if the token exists, false otherwise
    function exists(uint256 _tokenId) external view returns (bool) {
        return _exists(_tokenId);
    }

    /// @notice Returns the total number of tokens that have been minted
    /// @return The total number of minted tokens (including burned ones)
    function totalMinted() external view returns (uint256) {
        return _nextTokenId - 1;
    }

    /// @notice Transfers ownership of multiple tokens
    /// @param _from The current owner
    /// @param _to The new owner
    /// @param _tokenIds Array of token IDs to transfer
    function batchTransferFrom(
        address _from,
        address _to,
        uint256[] calldata _tokenIds
    ) external {
        for (uint256 i = 0; i < _tokenIds.length; i++) {
            transferFrom(_from, _to, _tokenIds[i]);
        }
    }

    /// @notice Safe transfers ownership of multiple tokens
    /// @param _from The current owner
    /// @param _to The new owner
    /// @param _tokenIds Array of token IDs to transfer
    function safeBatchTransferFrom(
        address _from,
        address _to,
        uint256[] calldata _tokenIds
    ) external {
        for (uint256 i = 0; i < _tokenIds.length; i++) {
            safeTransferFrom(_from, _to, _tokenIds[i]);
        }
    }

    /// @notice Safe transfers ownership of multiple tokens with data
    /// @param _from The current owner
    /// @param _to The new owner
    /// @param _tokenIds Array of token IDs to transfer
    /// @param _data Additional data to pass to the receiver
    function safeBatchTransferFrom(
        address _from,
        address _to,
        uint256[] calldata _tokenIds,
        bytes calldata _data
    ) external {
        for (uint256 i = 0; i < _tokenIds.length; i++) {
            safeTransferFrom(_from, _to, _tokenIds[i], _data);
        }
    }

    /// @notice Override to support both ERC721 and ERC721Enumerable
    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC721, ERC721Enumerable)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }

    /// @notice Override required by Solidity
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 tokenId
    ) internal override(ERC721, ERC721Enumerable) {
        super._beforeTokenTransfer(from, to, tokenId);
    }

    /// @notice Emergency function to recover accidentally sent tokens
    /// @param _token The token contract address
    /// @param _to The address to send the tokens to
    /// @param _amount The amount of tokens to recover
    function recoverERC20(address _token, address _to, uint256 _amount) external onlyOwner {
        IERC20(_token).transfer(_to, _amount);
    }

    /// @notice Emergency function to recover accidentally sent NFTs
    /// @param _token The NFT contract address
    /// @param _to The address to send the NFT to
    /// @param _tokenId The token ID to recover
    function recoverERC721(address _token, address _to, uint256 _tokenId) external onlyOwner {
        IERC721(_token).transferFrom(address(this), _to, _tokenId);
    }
}

// Import IERC20 and IERC721 for recovery functions
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC721} from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
