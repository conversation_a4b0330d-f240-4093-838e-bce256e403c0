// SPDX-License-Identifier: AGPL-3.0-or-later
pragma solidity 0.8.17;

import {Plugin, IDAO} from "@aragon/osx/core/plugin/Plugin.sol";
import {PluginUUPSUpgradeable} from "@aragon/osx/core/plugin/PluginUUPSUpgradeable.sol";
import {PermissionLib} from "@aragon/osx/core/permission/PermissionLib.sol";
import {IERC721} from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import {IERC165} from "@openzeppelin/contracts/utils/introspection/IERC165.sol";
import {SafeCast} from "@openzeppelin/contracts/utils/math/SafeCast.sol";
import {Initializable} from "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import {NftVotingLogic} from "./NftVotingLogic.sol";

/// @title NftVotingPlugin
/// <AUTHOR>
/// @notice A plugin that allows NFT holders to vote on DAO proposals using ERC-721 tokens
/// @dev Each NFT token represents 1 vote and can only be used once per proposal
contract NftVotingPlugin is PluginUUPSUpgradeable {
    using SafeCast for uint256;

    /// @notice The permission identifier to create proposals
    bytes32 public constant CREATE_PROPOSAL_PERMISSION_ID = keccak256("CREATE_PROPOSAL_PERMISSION");

    /// @notice The permission identifier to execute proposals
    bytes32 public constant EXECUTE_PROPOSAL_PERMISSION_ID = keccak256("EXECUTE_PROPOSAL_PERMISSION");

    /// @notice The permission identifier to update voting settings
    bytes32 public constant UPDATE_VOTING_SETTINGS_PERMISSION_ID = keccak256("UPDATE_VOTING_SETTINGS_PERMISSION");

    /// @notice A container for the voting settings that will be applied as parameters on proposal creation
    /// @param votingMode A parameter to select the vote mode
    /// @param supportThreshold The support threshold value (in basis points, 1000000 = 100%)
    /// @param minParticipation The minimum participation value (in basis points, 1000000 = 100%)
    /// @param minDuration The minimum duration of the proposal vote in seconds
    /// @param minProposerVotingPower The minimum voting power required to create a proposal
    struct VotingSettings {
        VotingMode votingMode;
        uint32 supportThreshold;
        uint32 minParticipation;
        uint64 minDuration;
        uint256 minProposerVotingPower;
    }

    /// @notice A container for proposal-related information
    /// @param executed Whether the proposal is executed or not
    /// @param parameters The proposal parameters at the time of the proposal creation
    /// @param tally The vote tally of the proposal
    /// @param voters The voters of the proposal
    /// @param actions The actions to be executed when the proposal passes
    /// @param allowFailureMap A bitmap allowing the proposal to succeed, even if individual actions might revert
    struct Proposal {
        bool executed;
        ProposalParameters parameters;
        Tally tally;
        mapping(uint256 => bool) voters; // tokenId => hasVoted
        IDAO.Action[] actions;
        uint256 allowFailureMap;
    }

    /// @notice A container for the proposal parameters at the time of proposal creation
    /// @param votingMode A parameter to select the vote mode
    /// @param supportThreshold The support threshold value (in basis points, 1000000 = 100%)
    /// @param minParticipation The minimum participation value (in basis points, 1000000 = 100%)
    /// @param startDate The start date of the proposal vote
    /// @param endDate The end date of the proposal vote
    /// @param snapshotBlock The number of the block prior to the proposal creation
    /// @param minProposerVotingPower The minimum voting power required to create a proposal
    struct ProposalParameters {
        VotingMode votingMode;
        uint32 supportThreshold;
        uint32 minParticipation;
        uint64 startDate;
        uint64 endDate;
        uint64 snapshotBlock;
        uint256 minProposerVotingPower;
    }

    /// @notice A container for the proposal vote tally
    /// @param abstain The number of abstain votes
    /// @param yes The number of yes votes
    /// @param no The number of no votes
    struct Tally {
        uint256 abstain;
        uint256 yes;
        uint256 no;
    }

    /// @notice The different voting modes available
    /// @param Standard Standard voting mode where only yes, no, and abstain votes are allowed
    /// @param EarlyExecution Early execution mode where proposals can be executed early if they reach the required threshold
    /// @param VoteReplacement Vote replacement mode where voters can change their vote
    enum VotingMode {
        Standard,
        EarlyExecution,
        VoteReplacement
    }

    /// @notice The different vote options available
    enum VoteOption {
        None,
        Abstain,
        Yes,
        No
    }

    /// @notice The ERC-721 token used for voting
    IERC721 public nftToken;

    /// @notice The current voting settings
    VotingSettings public votingSettings;

    /// @notice The proposal counter
    uint256 public proposalCounter;

    /// @notice The mapping from proposal ID to proposal information
    mapping(uint256 => Proposal) internal proposals;

    /// @notice Thrown if a date is out of bounds
    /// @param limit The limit value
    /// @param actual The actual value
    error DateOutOfBounds(uint64 limit, uint64 actual);

    /// @notice Thrown if the minimal duration value is out of bounds (less than one hour or greater than 1 year)
    /// @param limit The limit value
    /// @param actual The actual value
    error MinDurationOutOfBounds(uint64 limit, uint64 actual);

    /// @notice Thrown when a sender is not allowed to create a proposal
    /// @param sender The sender address
    error ProposalCreationForbidden(address sender);

    /// @notice Thrown if the proposal execution is forbidden
    /// @param proposalId The proposal identifier
    error ProposalExecutionForbidden(uint256 proposalId);

    /// @notice Thrown if the support threshold value is not supported
    /// @param limit The limit value
    /// @param actual The actual value
    error SupportThresholdOutOfBounds(uint32 limit, uint32 actual);

    /// @notice Thrown if the minimum participation value is not supported
    /// @param limit The limit value
    /// @param actual The actual value
    error MinParticipationOutOfBounds(uint32 limit, uint32 actual);

    /// @notice Thrown when the vote is cast by someone who doesn't own the NFT
    /// @param tokenId The token ID
    /// @param voter The voter address
    error VoteCastForbidden(uint256 tokenId, address voter);

    /// @notice Thrown if the voting for a proposal has not started yet
    /// @param proposalId The proposal identifier
    /// @param currentTime The current time
    /// @param startDate The start date of the proposal
    error VotingNotStarted(uint256 proposalId, uint64 currentTime, uint64 startDate);

    /// @notice Thrown if the voting for a proposal has ended
    /// @param proposalId The proposal identifier
    /// @param currentTime The current time
    /// @param endDate The end date of the proposal
    error VotingEnded(uint256 proposalId, uint64 currentTime, uint64 endDate);

    /// @notice Thrown if the token has already been used to vote on the proposal
    /// @param proposalId The proposal identifier
    /// @param tokenId The token ID
    error TokenAlreadyVoted(uint256 proposalId, uint256 tokenId);

    /// @notice Emitted when a proposal is created
    /// @param proposalId The proposal identifier
    /// @param creator The creator of the proposal
    /// @param startDate The start date of the proposal
    /// @param endDate The end date of the proposal
    /// @param metadata The metadata of the proposal
    /// @param actions The actions to be executed if the proposal passes
    /// @param allowFailureMap The failure map of the proposal
    event ProposalCreated(
        uint256 indexed proposalId,
        address indexed creator,
        uint64 startDate,
        uint64 endDate,
        bytes metadata,
        IDAO.Action[] actions,
        uint256 allowFailureMap
    );

    /// @notice Emitted when a vote is cast
    /// @param proposalId The proposal identifier
    /// @param voter The voter address
    /// @param tokenId The token ID used for voting
    /// @param voteOption The vote option
    event VoteCast(uint256 indexed proposalId, address indexed voter, uint256 indexed tokenId, VoteOption voteOption);

    /// @notice Emitted when a proposal is executed
    /// @param proposalId The proposal identifier
    event ProposalExecuted(uint256 indexed proposalId);

    /// @notice Emitted when the voting settings are updated
    /// @param votingMode The new voting mode
    /// @param supportThreshold The new support threshold
    /// @param minParticipation The new minimum participation
    /// @param minDuration The new minimum duration
    /// @param minProposerVotingPower The new minimum proposer voting power
    event VotingSettingsUpdated(
        VotingMode votingMode,
        uint32 supportThreshold,
        uint32 minParticipation,
        uint64 minDuration,
        uint256 minProposerVotingPower
    );

    /// @notice Disables the initializers on the implementation contract to prevent misuse
    constructor() {
        _disableInitializers();
    }

    /// @notice Initializes the plugin
    /// @param _dao The associated DAO
    /// @param _nftToken The ERC-721 token used for voting
    /// @param _votingSettings The voting settings
    function initialize(
        IDAO _dao,
        IERC721 _nftToken,
        VotingSettings memory _votingSettings
    ) external initializer {
        __PluginUUPSUpgradeable_init(_dao);
        nftToken = _nftToken;
        _updateVotingSettings(_votingSettings);
    }

    /// @notice Checks if this or the parent contract supports an interface by its ID
    /// @param _interfaceId The ID of the interface
    /// @return Returns `true` if the interface is supported
    function supportsInterface(bytes4 _interfaceId) public view virtual override returns (bool) {
        return _interfaceId == type(NftVotingPlugin).interfaceId || super.supportsInterface(_interfaceId);
    }

    /// @notice Updates the voting settings
    /// @param _votingSettings The new voting settings
    function updateVotingSettings(VotingSettings calldata _votingSettings)
        external
        auth(UPDATE_VOTING_SETTINGS_PERMISSION_ID)
    {
        _updateVotingSettings(_votingSettings);
    }

    /// @notice Creates a new proposal
    /// @param _metadata The metadata of the proposal
    /// @param _actions The actions to be executed if the proposal passes
    /// @param _allowFailureMap The failure map of the proposal
    /// @param _startDate The start date of the proposal vote
    /// @param _endDate The end date of the proposal vote
    /// @return proposalId The ID of the newly created proposal
    function createProposal(
        bytes calldata _metadata,
        IDAO.Action[] calldata _actions,
        uint256 _allowFailureMap,
        uint64 _startDate,
        uint64 _endDate
    ) external auth(CREATE_PROPOSAL_PERMISSION_ID) returns (uint256 proposalId) {
        uint64 snapshotBlock = block.number.toUint64() - 1;

        if (_startDate == 0) {
            _startDate = block.timestamp.toUint64();
        } else {
            if (_startDate < block.timestamp.toUint64()) {
                revert DateOutOfBounds({limit: block.timestamp.toUint64(), actual: _startDate});
            }
        }

        if (_endDate < _startDate + votingSettings.minDuration) {
            revert DateOutOfBounds({limit: _startDate + votingSettings.minDuration, actual: _endDate});
        }

        // Check if the proposer has enough voting power
        uint256 proposerVotingPower = _getVotingPower(msg.sender, snapshotBlock);
        if (proposerVotingPower < votingSettings.minProposerVotingPower) {
            revert ProposalCreationForbidden(msg.sender);
        }

        proposalId = ++proposalCounter;

        Proposal storage proposal_ = proposals[proposalId];

        proposal_.parameters.votingMode = votingSettings.votingMode;
        proposal_.parameters.supportThreshold = votingSettings.supportThreshold;
        proposal_.parameters.minParticipation = votingSettings.minParticipation;
        proposal_.parameters.snapshotBlock = snapshotBlock;
        proposal_.parameters.startDate = _startDate;
        proposal_.parameters.endDate = _endDate;
        proposal_.parameters.minProposerVotingPower = votingSettings.minProposerVotingPower;
        proposal_.allowFailureMap = _allowFailureMap;

        for (uint256 i; i < _actions.length; ) {
            proposal_.actions.push(_actions[i]);
            unchecked {
                ++i;
            }
        }

        emit ProposalCreated({
            proposalId: proposalId,
            creator: msg.sender,
            startDate: _startDate,
            endDate: _endDate,
            metadata: _metadata,
            actions: _actions,
            allowFailureMap: _allowFailureMap
        });
    }

    /// @notice Votes on a proposal using an NFT token
    /// @param _proposalId The ID of the proposal
    /// @param _tokenId The ID of the NFT token to use for voting
    /// @param _voteOption The vote option (Yes, No, Abstain)
    function vote(uint256 _proposalId, uint256 _tokenId, VoteOption _voteOption) external {
        Proposal storage proposal_ = proposals[_proposalId];

        // Check if the voter owns the NFT
        if (nftToken.ownerOf(_tokenId) != msg.sender) {
            revert VoteCastForbidden({tokenId: _tokenId, voter: msg.sender});
        }

        // Check if voting has started
        if (block.timestamp < proposal_.parameters.startDate) {
            revert VotingNotStarted({
                proposalId: _proposalId,
                currentTime: block.timestamp.toUint64(),
                startDate: proposal_.parameters.startDate
            });
        }

        // Check if voting has ended
        if (block.timestamp >= proposal_.parameters.endDate) {
            revert VotingEnded({
                proposalId: _proposalId,
                currentTime: block.timestamp.toUint64(),
                endDate: proposal_.parameters.endDate
            });
        }

        // Check if the token has already been used to vote
        if (proposal_.voters[_tokenId]) {
            revert TokenAlreadyVoted({proposalId: _proposalId, tokenId: _tokenId});
        }

        // Mark the token as having voted
        proposal_.voters[_tokenId] = true;

        // Update the tally
        if (_voteOption == VoteOption.Yes) {
            proposal_.tally.yes += 1;
        } else if (_voteOption == VoteOption.No) {
            proposal_.tally.no += 1;
        } else if (_voteOption == VoteOption.Abstain) {
            proposal_.tally.abstain += 1;
        }

        emit VoteCast({proposalId: _proposalId, voter: msg.sender, tokenId: _tokenId, voteOption: _voteOption});
    }

    /// @notice Executes a proposal if it has passed
    /// @param _proposalId The ID of the proposal to execute
    function execute(uint256 _proposalId) external {
        Proposal storage proposal_ = proposals[_proposalId];

        if (!_canExecute(_proposalId)) {
            revert ProposalExecutionForbidden(_proposalId);
        }

        proposal_.executed = true;

        _execute(bytes32(_proposalId), proposal_.actions, proposal_.allowFailureMap);

        emit ProposalExecuted(_proposalId);
    }

    /// @notice Returns whether a proposal can be executed
    /// @param _proposalId The ID of the proposal
    /// @return True if the proposal can be executed, false otherwise
    function canExecute(uint256 _proposalId) external view returns (bool) {
        return _canExecute(_proposalId);
    }

    /// @notice Returns whether a proposal has passed
    /// @param _proposalId The ID of the proposal
    /// @return True if the proposal has passed, false otherwise
    function hasProposalPassed(uint256 _proposalId) external view returns (bool) {
        return _hasProposalPassed(_proposalId);
    }

    /// @notice Returns the proposal information
    /// @param _proposalId The ID of the proposal
    /// @return executed Whether the proposal is executed
    /// @return parameters The proposal parameters
    /// @param tally The vote tally
    /// @param actions The actions to be executed
    /// @param allowFailureMap The failure map
    function getProposal(uint256 _proposalId)
        external
        view
        returns (
            bool executed,
            ProposalParameters memory parameters,
            Tally memory tally,
            IDAO.Action[] memory actions,
            uint256 allowFailureMap
        )
    {
        Proposal storage proposal_ = proposals[_proposalId];

        executed = proposal_.executed;
        parameters = proposal_.parameters;
        tally = proposal_.tally;
        allowFailureMap = proposal_.allowFailureMap;

        actions = new IDAO.Action[](proposal_.actions.length);
        for (uint256 i; i < proposal_.actions.length; ) {
            actions[i] = proposal_.actions[i];
            unchecked {
                ++i;
            }
        }
    }

    /// @notice Returns whether a token has voted on a proposal
    /// @param _proposalId The ID of the proposal
    /// @param _tokenId The ID of the token
    /// @return True if the token has voted, false otherwise
    function hasTokenVoted(uint256 _proposalId, uint256 _tokenId) external view returns (bool) {
        return proposals[_proposalId].voters[_tokenId];
    }

    /// @notice Returns the total voting power at a specific block
    /// @param _blockNumber The block number
    /// @return The total voting power (total supply of NFTs)
    function totalVotingPower(uint256 _blockNumber) external view returns (uint256) {
        // For ERC-721, we would need to track total supply at specific blocks
        // This is a simplified implementation
        return _getTotalVotingPower(_blockNumber);
    }

    /// @notice Returns the voting power of an account at a specific block
    /// @param _account The account address
    /// @param _blockNumber The block number
    /// @return The voting power (number of NFTs owned)
    function getVotingPower(address _account, uint256 _blockNumber) external view returns (uint256) {
        return _getVotingPower(_account, _blockNumber);
    }

    /// @notice Internal function to update voting settings
    /// @param _votingSettings The new voting settings
    function _updateVotingSettings(VotingSettings memory _votingSettings) internal {
        // Validate support threshold (must be <= 100%)
        if (_votingSettings.supportThreshold > 1000000) {
            revert SupportThresholdOutOfBounds({limit: 1000000, actual: _votingSettings.supportThreshold});
        }

        // Validate minimum participation (must be <= 100%)
        if (_votingSettings.minParticipation > 1000000) {
            revert MinParticipationOutOfBounds({limit: 1000000, actual: _votingSettings.minParticipation});
        }

        // Validate minimum duration (between 1 hour and 1 year)
        if (_votingSettings.minDuration < 1 hours || _votingSettings.minDuration > 365 days) {
            revert MinDurationOutOfBounds({limit: 365 days, actual: _votingSettings.minDuration});
        }

        votingSettings = _votingSettings;

        emit VotingSettingsUpdated({
            votingMode: _votingSettings.votingMode,
            supportThreshold: _votingSettings.supportThreshold,
            minParticipation: _votingSettings.minParticipation,
            minDuration: _votingSettings.minDuration,
            minProposerVotingPower: _votingSettings.minProposerVotingPower
        });
    }

    /// @notice Internal function to check if a proposal can be executed
    /// @param _proposalId The ID of the proposal
    /// @return True if the proposal can be executed, false otherwise
    function _canExecute(uint256 _proposalId) internal view returns (bool) {
        Proposal storage proposal_ = proposals[_proposalId];

        // Check if already executed
        if (proposal_.executed) {
            return false;
        }

        // Check if voting has ended
        if (block.timestamp < proposal_.parameters.endDate) {
            // Early execution is only allowed in EarlyExecution mode
            if (proposal_.parameters.votingMode != VotingMode.EarlyExecution) {
                return false;
            }
        }

        return _hasProposalPassed(_proposalId);
    }

    /// @notice Internal function to check if a proposal has passed
    /// @param _proposalId The ID of the proposal
    /// @return True if the proposal has passed, false otherwise
    function _hasProposalPassed(uint256 _proposalId) internal view returns (bool) {
        Proposal storage proposal_ = proposals[_proposalId];

        uint256 totalVotes = proposal_.tally.yes + proposal_.tally.no + proposal_.tally.abstain;
        uint256 totalVotingPower_ = _getTotalVotingPower(proposal_.parameters.snapshotBlock);

        // Check minimum participation
        if (totalVotes * 1000000 < totalVotingPower_ * proposal_.parameters.minParticipation) {
            return false;
        }

        // Check support threshold (yes votes / (yes + no votes))
        uint256 supportVotes = proposal_.tally.yes + proposal_.tally.no;
        if (supportVotes == 0) {
            return false;
        }

        return proposal_.tally.yes * 1000000 >= supportVotes * proposal_.parameters.supportThreshold;
    }

    /// @notice Internal function to get voting power of an account at a specific block
    /// @param _account The account address
    /// @param _blockNumber The block number
    /// @return The voting power (simplified implementation)
    function _getVotingPower(address _account, uint256 _blockNumber) internal view returns (uint256) {
        // This is a simplified implementation
        // In a real implementation, you would need to track NFT ownership at specific blocks
        _blockNumber; // Silence unused parameter warning
        return nftToken.balanceOf(_account);
    }

    /// @notice Internal function to get total voting power at a specific block
    /// @param _blockNumber The block number
    /// @return The total voting power (simplified implementation)
    function _getTotalVotingPower(uint256 _blockNumber) internal view returns (uint256) {
        // This is a simplified implementation
        // In a real implementation, you would need to track total supply at specific blocks
        _blockNumber; // Silence unused parameter warning

        // For now, we'll use a simple approach - this would need to be improved
        // to track historical total supply properly
        try IERC165(address(nftToken)).supportsInterface(0x780e9d63) returns (bool supportsEnumerable) {
            if (supportsEnumerable) {
                // If the contract supports ERC721Enumerable, we can get total supply
                // This is a hack and should be replaced with proper interface
                (bool success, bytes memory data) = address(nftToken).staticcall(
                    abi.encodeWithSignature("totalSupply()")
                );
                if (success && data.length == 32) {
                    return abi.decode(data, (uint256));
                }
            }
        } catch {}

        // Fallback: return a large number (this should be improved)
        return 10000;
    }
}
