# NFT Voting Plugin for Aragon OSx

A comprehensive Aragon OSx plugin that enables NFT-based voting using ERC-721 tokens, where each NFT represents one vote and can only be used once per proposal.

## 🌟 Features

- **NFT-Based Voting**: Each ERC-721 token represents exactly 1 vote
- **Double Voting Prevention**: Each token can only vote once per proposal
- **Role Management**: Built-in support for Manager and Council role appointments
- **Configurable Thresholds**: Customizable support and participation requirements
- **Multiple Voting Modes**: Standard, Early Execution, and Vote Replacement modes
- **Automatic Execution**: Proposals automatically execute via `dao.execute()` when passed
- **Comprehensive Testing**: Full test suite with integration tests

## 🏗️ Architecture

### Core Contracts

- **`NftVotingPlugin.sol`**: Main plugin contract implementing voting logic
- **`NftVotingPluginSetup.sol`**: Setup contract for plugin installation
- **`RoleProposalHelper.sol`**: Helper for creating role-based proposals
- **`NftVotingLogic.sol`**: Library containing core voting algorithms
- **`INftVotingPlugin.sol`**: Interface definition

### Test Contracts

- **`MockNFT.sol`**: ERC-721 contract for testing
- **`MockDAO.sol`**: Simplified DAO implementation for testing

## 🚀 Quick Start

### Prerequisites

- Node.js >= 16
- Hardhat
- An ERC-721 NFT contract
- An Aragon DAO

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd aragon-nft-voting-plugin

# Install dependencies
npm install

# Compile contracts
npm run build

# Run tests
npm test
```

### Deployment

1. **Deploy the plugin contracts:**
```bash
npx hardhat run scripts/deploy-plugin.ts --network <network>
```

2. **Install to a DAO:**
```bash
# Set environment variables
export DAO_ADDRESS=0x...
export NFT_CONTRACT_ADDRESS=0x...
export PLUGIN_SETUP_ADDRESS=0x...

# Run installation
npx hardhat run scripts/install-plugin.ts --network <network>
```

3. **Complete setup (deploy + install + test):**
```bash
# Set configuration
export DEPLOY_CONTRACTS=true
export INSTALL_TO_DAO=true
export RUN_TESTS=true
export CREATE_TEST_NFT=true

# Run complete setup
npx hardhat run scripts/setup-complete.ts --network <network>
```

## ⚙️ Configuration

### Voting Settings

```typescript
interface VotingSettings {
  votingMode: VotingMode;           // 0=Standard, 1=EarlyExecution, 2=VoteReplacement
  supportThreshold: number;         // In basis points (500000 = 50%)
  minParticipation: number;         // In basis points (250000 = 25%)
  minDuration: number;              // Minimum voting duration in seconds
  minProposerVotingPower: number;   // Minimum NFTs required to create proposal
}
```

### Environment Variables

```bash
# Deployment
MNEMONIC="your mnemonic here"
INFURA_API_KEY=your_infura_key
ETHERSCAN_API_KEY=your_etherscan_key

# Plugin Configuration
DAO_ADDRESS=0x...
NFT_CONTRACT_ADDRESS=0x...
VOTING_MODE=0
SUPPORT_THRESHOLD=500000
MIN_PARTICIPATION=250000
MIN_DURATION=86400
MIN_PROPOSER_VOTING_POWER=1

# Setup Options
DEPLOY_CONTRACTS=true
INSTALL_TO_DAO=false
RUN_TESTS=true
CREATE_TEST_NFT=false
```

## 📖 Usage

### Creating Proposals

#### Standard Proposal
```solidity
bytes memory metadata = abi.encode("Proposal title", "Description");
IDAO.Action[] memory actions = new IDAO.Action[](1);
actions[0] = IDAO.Action({
    to: targetContract,
    value: 0,
    data: abi.encodeWithSelector(targetFunction.selector, params)
});

uint256 proposalId = plugin.createProposal(
    metadata,
    actions,
    0, // allowFailureMap
    startDate,
    endDate
);
```

#### Manager Appointment Proposal
```solidity
// Using RoleProposalHelper
IDAO.Action[] memory actions = roleHelper.createManagerAppointmentActions(
    daoAddress,
    managerAddress
);

bytes memory metadata = roleHelper.createRoleProposalMetadata(
    RoleProposalHelper.ProposalType.ManagerAppointment,
    [managerAddress],
    "Appointing new DAO Manager"
);

uint256 proposalId = plugin.createProposal(metadata, actions, 0, startDate, endDate);
```

### Voting

```solidity
// Vote with an owned NFT
plugin.vote(
    proposalId,
    tokenId,
    VoteOption.Yes  // 2 = Yes, 3 = No, 1 = Abstain
);
```

### Executing Proposals

```solidity
// Check if proposal can be executed
bool canExecute = plugin.canExecute(proposalId);

// Execute if passed
if (canExecute) {
    plugin.execute(proposalId);
}
```

## 🔐 Permissions

The plugin requires the following permissions:

- **`CREATE_PROPOSAL_PERMISSION`**: Allows creating proposals (granted to DAO)
- **`EXECUTE_PERMISSION`**: Allows plugin to execute actions on DAO (granted to plugin)
- **`UPDATE_VOTING_SETTINGS_PERMISSION`**: Allows updating voting parameters (granted to DAO)
- **`EXECUTE_PROPOSAL_PERMISSION`**: Allows manual proposal execution (granted to DAO)

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
npx hardhat test test/integration/FullIntegration.test.ts
```

### Test Plugin Functionality
```bash
npx hardhat run scripts/test-plugin.ts
```

## 📊 Voting Mechanics

### Vote Counting
- Each ERC-721 token = 1 vote
- Tokens can only vote once per proposal
- Vote options: Yes, No, Abstain

### Threshold Calculations
- **Support Threshold**: `yes_votes / (yes_votes + no_votes) >= threshold`
- **Participation Threshold**: `total_votes / total_nft_supply >= threshold`

### Execution Conditions
1. Voting period has ended (or early execution enabled)
2. Minimum participation threshold met
3. Support threshold met
4. Proposal not already executed

## 🛠️ Development

### Project Structure
```
aragon-nft-voting-plugin/
├── contracts/
│   ├── NftVotingPlugin.sol
│   ├── NftVotingPluginSetup.sol
│   ├── RoleProposalHelper.sol
│   ├── NftVotingLogic.sol
│   ├── INftVotingPlugin.sol
│   └── test/
├── scripts/
│   ├── deploy-plugin.ts
│   ├── install-plugin.ts
│   ├── test-plugin.ts
│   └── setup-complete.ts
├── test/
│   ├── NftVotingPlugin.test.ts
│   ├── RoleProposalHelper.test.ts
│   └── integration/
└── docs/
```

### Adding New Features

1. Implement in the main contract
2. Add comprehensive tests
3. Update interfaces and documentation
4. Test integration scenarios

## 🌐 Deployment Networks

### Supported Networks
- Ethereum Mainnet
- Polygon
- Sepolia Testnet
- Polygon Mumbai

### Gas Estimates
- Plugin Deployment: ~2,500,000 gas
- Proposal Creation: ~200,000 gas
- Voting: ~80,000 gas
- Execution: ~150,000 gas (+ action costs)

## 📚 Documentation

- [User Guide](./docs/user-guide.md)
- [Developer Guide](./docs/developer-guide.md)
- [API Reference](./docs/api.md)
- [Aragon OSx Documentation](https://docs.aragon.org/osx/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under AGPL-3.0-or-later - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- [Aragon OSx](https://github.com/aragon/osx)
- [Plugin Development Guide](https://docs.aragon.org/osx/how-to-guides/plugin-development/)
- [Aragon Governance](https://aragon.org/)

## ⚠️ Security Considerations

- Ensure NFT contract is trusted and immutable
- Verify voting thresholds are appropriate for your use case
- Test thoroughly before mainnet deployment
- Consider implementing timelock for critical proposals
- Monitor for unusual voting patterns

## 🆘 Support

For questions and support:
- Create an issue in this repository
- Join the [Aragon Discord](https://discord.gg/aragon)
- Check the [Aragon Forum](https://forum.aragon.org/)
