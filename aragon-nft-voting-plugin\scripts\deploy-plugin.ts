import {ethers} from 'hardhat';
import {
  PLUGIN_CONTRACT_NAME,
  PLUGIN_SETUP_CONTRACT_NAME,
  VERSION,
  DEFAULT_VOTING_SETTINGS,
} from '../plugin-settings';
import {
  PLUGIN_METADATA,
  PLUGIN_SETUP_METADATA,
  RELEASE_METADATA,
  BUILD_METADATA,
} from '../plugin-metadata';

async function main() {
  console.log('🚀 Starting NFT Voting Plugin deployment...\n');

  const [deployer] = await ethers.getSigners();
  console.log('Deploying with account:', deployer.address);
  console.log('Account balance:', ethers.utils.formatEther(await deployer.getBalance()), 'ETH\n');

  // Get network information
  const network = await ethers.provider.getNetwork();
  console.log('Network:', network.name);
  console.log('Chain ID:', network.chainId);
  console.log('Block number:', await ethers.provider.getBlockNumber(), '\n');

  // Deploy the plugin implementation
  console.log('📦 Deploying plugin implementation...');
  const PluginFactory = await ethers.getContractFactory(PLUGIN_CONTRACT_NAME);
  const pluginImplementation = await PluginFactory.deploy();
  await pluginImplementation.deployed();

  console.log(`✅ ${PLUGIN_CONTRACT_NAME} deployed at:`, pluginImplementation.address);
  console.log('Gas used:', (await pluginImplementation.deployTransaction.wait()).gasUsed.toString());

  // Deploy the plugin setup
  console.log('\n📦 Deploying plugin setup...');
  const PluginSetupFactory = await ethers.getContractFactory(PLUGIN_SETUP_CONTRACT_NAME);
  const pluginSetup = await PluginSetupFactory.deploy();
  await pluginSetup.deployed();

  console.log(`✅ ${PLUGIN_SETUP_CONTRACT_NAME} deployed at:`, pluginSetup.address);
  console.log('Gas used:', (await pluginSetup.deployTransaction.wait()).gasUsed.toString());

  // Verify the implementation address in the setup contract
  const implementationAddress = await pluginSetup.implementation();
  console.log('\n🔍 Verifying setup contract...');
  console.log('Implementation address in setup:', implementationAddress);
  
  if (implementationAddress.toLowerCase() !== pluginImplementation.address.toLowerCase()) {
    console.warn('⚠️  Warning: Implementation address mismatch!');
  } else {
    console.log('✅ Implementation address verified');
  }

  // Display deployment summary
  console.log('\n📋 Deployment Summary:');
  console.log('='.repeat(50));
  console.log(`Plugin Implementation: ${pluginImplementation.address}`);
  console.log(`Plugin Setup:          ${pluginSetup.address}`);
  console.log(`Version:               ${VERSION.release}.${VERSION.build}`);
  console.log(`Network:               ${network.name} (${network.chainId})`);
  console.log(`Deployer:              ${deployer.address}`);
  console.log('='.repeat(50));

  // Save deployment information
  const deploymentInfo = {
    network: {
      name: network.name,
      chainId: network.chainId,
    },
    contracts: {
      pluginImplementation: {
        address: pluginImplementation.address,
        transactionHash: pluginImplementation.deployTransaction.hash,
      },
      pluginSetup: {
        address: pluginSetup.address,
        transactionHash: pluginSetup.deployTransaction.hash,
      },
    },
    version: VERSION,
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    metadata: {
      plugin: PLUGIN_METADATA,
      setup: PLUGIN_SETUP_METADATA,
      release: RELEASE_METADATA,
      build: BUILD_METADATA,
    },
  };

  // Write deployment info to file
  const fs = require('fs');
  const path = require('path');
  
  const deploymentsDir = path.join(__dirname, '..', 'deployments');
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, {recursive: true});
  }

  const deploymentFile = path.join(
    deploymentsDir,
    `deployment-${network.name}-${Date.now()}.json`
  );
  
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`\n💾 Deployment info saved to: ${deploymentFile}`);

  // Display next steps
  console.log('\n🎯 Next Steps:');
  console.log('1. Verify contracts on block explorer (if on public network)');
  console.log('2. Create plugin repository using Aragon OSx tools');
  console.log('3. Publish plugin to Aragon plugin registry');
  console.log('4. Install plugin to target DAO');
  console.log('\n📚 Documentation:');
  console.log('- Plugin Repository: https://docs.aragon.org/osx/how-to-guides/plugin-development/');
  console.log('- Plugin Installation: https://docs.aragon.org/osx/how-to-guides/plugin-management/');

  // Return deployment addresses for use in other scripts
  return {
    pluginImplementation: pluginImplementation.address,
    pluginSetup: pluginSetup.address,
    network: network.name,
    chainId: network.chainId,
  };
}

// Execute deployment if this script is run directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Deployment failed:', error);
      process.exit(1);
    });
}

export default main;
