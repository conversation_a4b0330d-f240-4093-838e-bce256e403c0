import {expect} from 'chai';
import {ethers} from 'hardhat';
import {Signer<PERSON>ithAddress} from '@nomiclabs/hardhat-ethers/signers';
import {RoleProposalHelper, MockDAO} from '../typechain';

describe('RoleProposalHelper', function () {
  let roleHelper: RoleProposalHelper;
  let mockDAO: MockDAO;
  let deployer: Signer<PERSON>ithAddress;
  let manager: <PERSON><PERSON><PERSON>ithAddress;
  let councilMember1: Signer<PERSON>ithAddress;
  let councilMember2: SignerWithAddress;

  beforeEach(async function () {
    [deployer, manager, councilMember1, councilMember2] = await ethers.getSigners();

    // Deploy mock DAO
    const MockDAOFactory = await ethers.getContractFactory('MockDAO');
    mockDAO = await MockDAOFactory.deploy();
    await mockDAO.deployed();

    // Deploy role proposal helper
    const RoleHelperFactory = await ethers.getContractFactory('RoleProposalHelper');
    roleHelper = await RoleHelperFactory.deploy();
    await roleHelper.deployed();
  });

  describe('Manager Appointment', function () {
    it('should create correct actions for manager appointment', async function () {
      const actions = await roleHelper.createManagerAppointmentActions(
        mockDAO.address,
        manager.address
      );

      expect(actions).to.have.length(1);
      expect(actions[0].to).to.equal(mockDAO.address);
      expect(actions[0].value).to.equal(0);

      // Decode the action data to verify it's calling grantRole
      const expectedData = mockDAO.interface.encodeFunctionData('grant', [
        mockDAO.address,
        manager.address,
        await roleHelper.MANAGER_ROLE(),
      ]);
      expect(actions[0].data).to.equal(expectedData);
    });

    it('should reject invalid manager address', async function () {
      await expect(
        roleHelper.createManagerAppointmentActions(
          mockDAO.address,
          ethers.constants.AddressZero
        )
      ).to.be.revertedWith('InvalidAddress');
    });
  });

  describe('Council Appointment', function () {
    it('should create correct actions for council appointment', async function () {
      const councilMembers = [councilMember1.address, councilMember2.address];
      const actions = await roleHelper.createCouncilAppointmentActions(
        mockDAO.address,
        councilMembers
      );

      expect(actions).to.have.length(2);

      for (let i = 0; i < actions.length; i++) {
        expect(actions[i].to).to.equal(mockDAO.address);
        expect(actions[i].value).to.equal(0);

        const expectedData = mockDAO.interface.encodeFunctionData('grant', [
          mockDAO.address,
          councilMembers[i],
          await roleHelper.COUNCIL_ROLE(),
        ]);
        expect(actions[i].data).to.equal(expectedData);
      }
    });

    it('should reject empty council members array', async function () {
      await expect(
        roleHelper.createCouncilAppointmentActions(mockDAO.address, [])
      ).to.be.revertedWith('InvalidAddress');
    });

    it('should reject invalid council member address', async function () {
      const councilMembers = [councilMember1.address, ethers.constants.AddressZero];
      await expect(
        roleHelper.createCouncilAppointmentActions(mockDAO.address, councilMembers)
      ).to.be.revertedWith('InvalidAddress');
    });
  });

  describe('Manager Removal', function () {
    it('should create correct actions for manager removal', async function () {
      const actions = await roleHelper.createManagerRemovalActions(
        mockDAO.address,
        manager.address
      );

      expect(actions).to.have.length(1);
      expect(actions[0].to).to.equal(mockDAO.address);
      expect(actions[0].value).to.equal(0);

      const expectedData = mockDAO.interface.encodeFunctionData('revoke', [
        mockDAO.address,
        manager.address,
        await roleHelper.MANAGER_ROLE(),
      ]);
      expect(actions[0].data).to.equal(expectedData);
    });

    it('should reject invalid manager address for removal', async function () {
      await expect(
        roleHelper.createManagerRemovalActions(
          mockDAO.address,
          ethers.constants.AddressZero
        )
      ).to.be.revertedWith('InvalidAddress');
    });
  });

  describe('Council Removal', function () {
    it('should create correct actions for council removal', async function () {
      const councilMembers = [councilMember1.address, councilMember2.address];
      const actions = await roleHelper.createCouncilRemovalActions(
        mockDAO.address,
        councilMembers
      );

      expect(actions).to.have.length(2);

      for (let i = 0; i < actions.length; i++) {
        expect(actions[i].to).to.equal(mockDAO.address);
        expect(actions[i].value).to.equal(0);

        const expectedData = mockDAO.interface.encodeFunctionData('revoke', [
          mockDAO.address,
          councilMembers[i],
          await roleHelper.COUNCIL_ROLE(),
        ]);
        expect(actions[i].data).to.equal(expectedData);
      }
    });

    it('should reject empty council members array for removal', async function () {
      await expect(
        roleHelper.createCouncilRemovalActions(mockDAO.address, [])
      ).to.be.revertedWith('InvalidAddress');
    });
  });

  describe('Proposal Metadata', function () {
    it('should create correct metadata for manager appointment', async function () {
      const targetAddresses = [manager.address];
      const description = 'Appointing new manager for the DAO';
      
      const metadata = await roleHelper.createRoleProposalMetadata(
        0, // ProposalType.ManagerAppointment
        targetAddresses,
        description
      );

      // Decode the metadata
      const decoded = ethers.utils.defaultAbiCoder.decode(
        ['string', 'string', 'string', 'address[]', 'uint8'],
        metadata
      );

      expect(decoded[0]).to.equal('Manager Appointment');
      expect(decoded[1]).to.equal('Proposal to appoint a new Manager');
      expect(decoded[2]).to.equal(description);
      expect(decoded[3]).to.deep.equal(targetAddresses);
      expect(decoded[4]).to.equal(0); // ProposalType.ManagerAppointment
    });

    it('should create correct metadata for council appointment', async function () {
      const targetAddresses = [councilMember1.address, councilMember2.address];
      const description = 'Appointing new council members';
      
      const metadata = await roleHelper.createRoleProposalMetadata(
        1, // ProposalType.CouncilAppointment
        targetAddresses,
        description
      );

      const decoded = ethers.utils.defaultAbiCoder.decode(
        ['string', 'string', 'string', 'address[]', 'uint8'],
        metadata
      );

      expect(decoded[0]).to.equal('Council Member Appointment');
      expect(decoded[1]).to.equal('Proposal to appoint new Council Members');
      expect(decoded[2]).to.equal(description);
      expect(decoded[3]).to.deep.equal(targetAddresses);
      expect(decoded[4]).to.equal(1); // ProposalType.CouncilAppointment
    });
  });

  describe('Proposal Validation', function () {
    it('should validate correct manager appointment proposal', async function () {
      const params = {
        proposalType: 0, // ManagerAppointment
        targetAddress: manager.address,
        role: await roleHelper.MANAGER_ROLE(),
        isGrant: true,
      };

      expect(await roleHelper.validateRoleProposal(params)).to.be.true;
    });

    it('should validate correct council appointment proposal', async function () {
      const params = {
        proposalType: 1, // CouncilAppointment
        targetAddress: councilMember1.address,
        role: await roleHelper.COUNCIL_ROLE(),
        isGrant: true,
      };

      expect(await roleHelper.validateRoleProposal(params)).to.be.true;
    });

    it('should reject proposal with invalid address', async function () {
      const params = {
        proposalType: 0, // ManagerAppointment
        targetAddress: ethers.constants.AddressZero,
        role: await roleHelper.MANAGER_ROLE(),
        isGrant: true,
      };

      expect(await roleHelper.validateRoleProposal(params)).to.be.false;
    });

    it('should reject proposal with mismatched role and type', async function () {
      const params = {
        proposalType: 0, // ManagerAppointment
        targetAddress: manager.address,
        role: await roleHelper.COUNCIL_ROLE(), // Wrong role
        isGrant: true,
      };

      expect(await roleHelper.validateRoleProposal(params)).to.be.false;
    });

    it('should reject appointment proposal with isGrant=false', async function () {
      const params = {
        proposalType: 0, // ManagerAppointment
        targetAddress: manager.address,
        role: await roleHelper.MANAGER_ROLE(),
        isGrant: false, // Should be true for appointment
      };

      expect(await roleHelper.validateRoleProposal(params)).to.be.false;
    });

    it('should reject removal proposal with isGrant=true', async function () {
      const params = {
        proposalType: 2, // ManagerRemoval
        targetAddress: manager.address,
        role: await roleHelper.MANAGER_ROLE(),
        isGrant: true, // Should be false for removal
      };

      expect(await roleHelper.validateRoleProposal(params)).to.be.false;
    });
  });

  describe('Role Identification', function () {
    it('should return correct role for manager proposal types', async function () {
      expect(await roleHelper.getRoleForProposalType(0)).to.equal(await roleHelper.MANAGER_ROLE());
      expect(await roleHelper.getRoleForProposalType(2)).to.equal(await roleHelper.MANAGER_ROLE());
    });

    it('should return correct role for council proposal types', async function () {
      expect(await roleHelper.getRoleForProposalType(1)).to.equal(await roleHelper.COUNCIL_ROLE());
      expect(await roleHelper.getRoleForProposalType(3)).to.equal(await roleHelper.COUNCIL_ROLE());
    });

    it('should revert for invalid proposal type', async function () {
      await expect(roleHelper.getRoleForProposalType(4)).to.be.revertedWith('InvalidRole');
    });
  });
});
