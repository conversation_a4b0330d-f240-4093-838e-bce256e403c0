'use client';
import { useState } from 'react';

export default function Home() {
  const [email, setEmail] = useState('');
  const [wallet, setWallet] = useState('');
  const [agreed, setAgreed] = useState(false);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState<'register' | 'mint'>('register');
  const [mintResult, setMintResult] = useState<any>(null);

  const validateWallet = (address: string) => {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  };

  const registerEmail = async () => {
    if (!email || !email.includes('@')) return setMessage('Enter a valid email');
    if (!agreed) return setMessage('Please agree to privacy policy');

    setIsLoading(true);
    try {
      const res = await fetch('http://localhost:5000/register-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });

      const data = await res.text();
      setMessage(data);

      if (res.ok) {
        setTimeout(() => {
          setMode('mint');
          setMessage('');
        }, 2000);
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const mintNFT = async () => {
    if (!email || !email.includes('@')) return setMessage('Enter a valid email');
    if (!wallet || !validateWallet(wallet)) return setMessage('Enter a valid Ethereum wallet address (0x...)');
    if (!agreed) return setMessage('Please agree to privacy policy');

    setIsLoading(true);
    setMessage('Minting your NFT... This may take a moment.');

    try {
      const res = await fetch('http://localhost:5000/mint-nft', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, wallet }),
      });

      const data = await res.json();

      if (res.ok) {
        setMintResult(data);
        setMessage('🎉 NFT minted successfully!');
      } else {
        setMessage(data.error || 'Minting failed');
      }
    } catch (error) {
      setMessage('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-purple-50 to-blue-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold mb-6 text-center text-gray-800">
          {mode === 'register' ? 'Join the Waitlist' : 'Mint Your NFT Pass'}
        </h1>

        {mode === 'register' ? (
          // Email Registration Mode
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                className="w-full border border-gray-300 p-3 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>

            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={agreed}
                onChange={() => setAgreed(!agreed)}
                disabled={isLoading}
                className="rounded"
              />
              <span className="text-sm text-gray-600">I agree to the privacy policy</span>
            </label>

            <button
              onClick={registerEmail}
              disabled={isLoading}
              className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors"
            >
              {isLoading ? 'Registering...' : 'Join Waitlist'}
            </button>

            <button
              onClick={() => setMode('mint')}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Already registered? Mint NFT →
            </button>
          </div>
        ) : (
          // NFT Minting Mode
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                className="w-full border border-gray-300 p-3 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Wallet Address
              </label>
              <input
                className="w-full border border-gray-300 p-3 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent font-mono text-sm"
                type="text"
                placeholder="0x..."
                value={wallet}
                onChange={(e) => setWallet(e.target.value)}
                disabled={isLoading}
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter your Ethereum wallet address (42 characters starting with 0x)
              </p>
            </div>

            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={agreed}
                onChange={() => setAgreed(!agreed)}
                disabled={isLoading}
                className="rounded"
              />
              <span className="text-sm text-gray-600">I agree to the privacy policy</span>
            </label>

            <button
              onClick={mintNFT}
              disabled={isLoading}
              className="w-full bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors"
            >
              {isLoading ? 'Minting...' : '🎨 Mint NFT Pass'}
            </button>

            <button
              onClick={() => setMode('register')}
              className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              ← Back to Registration
            </button>
          </div>
        )}

        {message && (
          <div className={`mt-4 p-3 rounded-lg text-sm ${
            message.includes('successfully') || message.includes('🎉')
              ? 'bg-green-100 text-green-700 border border-green-200'
              : message.includes('Minting')
              ? 'bg-blue-100 text-blue-700 border border-blue-200'
              : 'bg-red-100 text-red-700 border border-red-200'
          }`}>
            {message}
          </div>
        )}

        {mintResult && (
          <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-bold text-green-800 mb-2">🎉 NFT Minted Successfully!</h3>
            <div className="space-y-2 text-sm text-green-700">
              <p><strong>Token ID:</strong> #{mintResult.tokenId}</p>
              <p><strong>Transaction:</strong>
                <span className="font-mono text-xs break-all">{mintResult.transactionHash}</span>
              </p>
              <p><strong>Name:</strong> {mintResult.metadata?.name}</p>
              <p><strong>Description:</strong> {mintResult.metadata?.description}</p>
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
