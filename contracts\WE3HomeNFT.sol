
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Strings.sol";

/**
 * @title WE3HomeNFT
 * @dev Production-ready ERC721 contract for WE3 Home NFTs
 *
 * Features:
 * - Max supply: 5200 NFTs
 * - Sequential token IDs (1-5200)
 * - Backend-controlled minting only
 * - One NFT per wallet limit
 * - Gas optimized (ERC721 instead of ERC721Enumerable)
 * - Event emission for Apple Wallet integration
 */
contract WE3HomeNFT is ERC721, Ownable {
    using Strings for uint256;

    // =============================================================================
    // CONSTANTS & STATE VARIABLES
    // =============================================================================

    uint256 public constant MAX_SUPPLY = 5200;
    uint256 public constant MAX_PER_WALLET = 1;

    string public baseTokenURI;
    address public backendAddress;

    mapping(address => bool) public hasMinted;
    uint256 private _tokenIdCounter;

    // =============================================================================
    // EVENTS
    // =============================================================================

    event Minted(address indexed recipient, uint256 indexed tokenId);
    event BackendAddressUpdated(address indexed oldBackend, address indexed newBackend);
    event BaseURIUpdated(string oldBaseURI, string newBaseURI);

    // =============================================================================
    // CONSTRUCTOR
    // =============================================================================

    constructor(string memory baseURI) ERC721("WE3 Home", "WE3") {
        require(bytes(baseURI).length > 0, "Base URI cannot be empty");
        baseTokenURI = baseURI;
    }

    // =============================================================================
    // ADMIN FUNCTIONS
    // =============================================================================

    /**
     * @dev Set the backend address that can control minting
     * @param _backendAddress Address of the backend controller
     */
    function setBackendAddress(address _backendAddress) external onlyOwner {
        require(_backendAddress != address(0), "Backend address cannot be zero");

        address oldBackend = backendAddress;
        backendAddress = _backendAddress;

        emit BackendAddressUpdated(oldBackend, _backendAddress);
    }

    /**
     * @dev Update the base URI for token metadata
     * @param baseURI New base URI for metadata
     */
    function setBaseURI(string memory baseURI) external onlyOwner {
        require(bytes(baseURI).length > 0, "Base URI cannot be empty");

        string memory oldBaseURI = baseTokenURI;
        baseTokenURI = baseURI;

        emit BaseURIUpdated(oldBaseURI, baseURI);
    }

    // =============================================================================
    // MINTING FUNCTIONS
    // =============================================================================

    /**
     * @dev Backend-controlled minting function
     * @param recipient Address to receive the NFT
     */
    function backendMint(address recipient) external {
        require(msg.sender == backendAddress, "Only backend can mint");
        require(backendAddress != address(0), "Backend address not set");
        require(recipient != address(0), "Cannot mint to zero address");
        require(_tokenIdCounter < MAX_SUPPLY, "Max supply reached");
        require(!hasMinted[recipient], "User already minted");

        // Gas optimization: unchecked increment since MAX_SUPPLY prevents overflow
        unchecked {
            _tokenIdCounter++;
        }
        uint256 tokenId = _tokenIdCounter;

        _safeMint(recipient, tokenId);
        hasMinted[recipient] = true;

        emit Minted(recipient, tokenId);
    }

    // =============================================================================
    // VIEW FUNCTIONS
    // =============================================================================

    /**
     * @dev Returns the token URI for a given token ID
     * @param tokenId Token ID to get URI for
     */
    function tokenURI(uint256 tokenId) public view override returns (string memory) {
        require(_ownerOf(tokenId) != address(0), "Token does not exist");
        return string(abi.encodePacked(baseTokenURI, tokenId.toString(), ".json"));
    }

    /**
     * @dev Returns the total number of tokens minted
     */
    function totalSupply() public view returns (uint256) {
        return _tokenIdCounter;
    }

    /**
     * @dev Returns the maximum supply of tokens
     */
    function maxSupply() public pure returns (uint256) {
        return MAX_SUPPLY;
    }

    /**
     * @dev Returns the maximum tokens per wallet
     */
    function maxPerWallet() public pure returns (uint256) {
        return MAX_PER_WALLET;
    }

    // =============================================================================
    // EMERGENCY FUNCTIONS
    // =============================================================================

    /**
     * @dev Emergency withdraw function for any ETH/MATIC sent to contract
     */
    function withdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");

        payable(owner()).transfer(balance);
    }

    /**
     * @dev Allow contract to receive ETH/MATIC for emergency scenarios
     */
    receive() external payable {
        // Contract can receive ETH/MATIC but only owner can withdraw
    }
}
