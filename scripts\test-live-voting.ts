import {ethers} from 'hardhat';

async function main() {
  console.log('🗳️  Testing Live Voting with Your Deployed Contracts');
  console.log('='.repeat(60));

  // Your deployed contract addresses
  const nftAddress = '******************************************';
  const votingAddress = '******************************************';
  
  // Connect to Polygon Amoy testnet
  const provider = new ethers.providers.JsonRpcProvider(process.env.POLYGON_TESTNET_RPC);
  const wallet = new ethers.Wallet(process.env.PRIVATE_KEY!, provider);
  
  console.log(`Connected to: Polygon Amoy Testnet`);
  console.log(`Wallet: ${wallet.address}`);
  console.log(`Balance: ${ethers.utils.formatEther(await wallet.getBalance())} MATIC\n`);

  // Get contract instances
  const nft = await ethers.getContractAt('WE3HomeNFT', nftAddress, wallet);
  const voting = await ethers.getContractAt('SimpleNftVoting', votingAddress, wallet);

  try {
    console.log('📊 Current Contract State:');
    console.log('-'.repeat(40));
    
    // NFT contract info
    const totalSupply = await nft.totalSupply();
    const maxSupply = await nft.maxSupply();
    console.log(`NFT Total Supply: ${totalSupply}`);
    console.log(`NFT Max Supply: ${maxSupply}`);
    
    // Voting contract info
    const proposalCounter = await voting.proposalCounter();
    const totalVotingPower = await voting.getTotalVotingPower();
    console.log(`Total Proposals: ${proposalCounter}`);
    console.log(`Total Voting Power: ${totalVotingPower}`);

    // List current NFT holders
    if (totalSupply.gt(0)) {
      console.log('\n👥 Current NFT Holders (DAO Members):');
      console.log('-'.repeat(40));
      
      for (let i = 1; i <= totalSupply.toNumber(); i++) {
        try {
          const tokenOwner = await nft.ownerOf(i);
          console.log(`Token ${i}: ${tokenOwner}`);
        } catch (error) {
          console.log(`Token ${i}: Error reading owner`);
        }
      }
    }

    // Create a test proposal
    console.log('\n📝 Creating Test Proposal...');
    console.log('-'.repeat(40));
    
    const proposalDescription = `Proposal ${Date.now()}: Should we implement a new feature for WE3Home DAO?`;
    console.log(`Description: ${proposalDescription}`);
    
    try {
      const createTx = await voting.createProposal(proposalDescription);
      console.log(`Transaction sent: ${createTx.hash}`);
      
      const receipt = await createTx.wait();
      console.log(`✅ Proposal created! Gas used: ${receipt.gasUsed.toString()}`);
      
      const newProposalId = await voting.proposalCounter();
      console.log(`Proposal ID: ${newProposalId}`);
      
      // Get proposal details
      const proposal = await voting.getProposal(newProposalId);
      console.log(`Start Time: ${new Date(proposal.startTime.toNumber() * 1000).toLocaleString()}`);
      console.log(`End Time: ${new Date(proposal.endTime.toNumber() * 1000).toLocaleString()}`);
      
    } catch (error) {
      console.log(`❌ Failed to create proposal: ${error}`);
    }

    // Check if we can vote (if we own an NFT)
    console.log('\n🗳️  Checking Voting Eligibility...');
    console.log('-'.repeat(40));
    
    const ourBalance = await nft.balanceOf(wallet.address);
    console.log(`Your NFT balance: ${ourBalance}`);
    
    if (ourBalance.gt(0)) {
      console.log('✅ You own NFTs and can vote!');
      
      // Find our token ID
      for (let i = 1; i <= totalSupply.toNumber(); i++) {
        try {
          const tokenOwner = await nft.ownerOf(i);
          if (tokenOwner.toLowerCase() === wallet.address.toLowerCase()) {
            console.log(`Your token ID: ${i}`);
            
            // Try to vote (if there's a proposal)
            const currentProposalId = await voting.proposalCounter();
            if (currentProposalId.gt(0)) {
              console.log('\n🗳️  Attempting to vote...');
              
              try {
                // Vote Yes (option 2)
                const voteTx = await voting.vote(currentProposalId, i, 2);
                console.log(`Vote transaction sent: ${voteTx.hash}`);
                
                const voteReceipt = await voteTx.wait();
                console.log(`✅ Vote cast! Gas used: ${voteReceipt.gasUsed.toString()}`);
                
                // Check updated proposal
                const updatedProposal = await voting.getProposal(currentProposalId);
                console.log(`Updated votes - Yes: ${updatedProposal.yesVotes}, No: ${updatedProposal.noVotes}, Abstain: ${updatedProposal.abstainVotes}`);
                
              } catch (error) {
                console.log(`❌ Voting failed: ${error}`);
              }
            }
            break;
          }
        } catch (error) {
          // Token doesn't exist or error reading
        }
      }
    } else {
      console.log('⚠️  You don\'t own any NFTs - cannot vote');
      console.log('To vote, you need to own a WE3Home NFT');
    }

    // Display current proposals
    const finalProposalCount = await voting.proposalCounter();
    if (finalProposalCount.gt(0)) {
      console.log('\n📋 Current Proposals:');
      console.log('-'.repeat(40));
      
      for (let i = 1; i <= finalProposalCount.toNumber(); i++) {
        const proposal = await voting.getProposal(i);
        const hasPassed = await voting.hasProposalPassed(i);
        
        console.log(`\nProposal ${i}:`);
        console.log(`  Description: ${proposal.description}`);
        console.log(`  Yes: ${proposal.yesVotes}, No: ${proposal.noVotes}, Abstain: ${proposal.abstainVotes}`);
        console.log(`  Status: ${proposal.executed ? 'Executed' : hasPassed ? 'Passed (can execute)' : 'Active/Failed'}`);
        console.log(`  End Time: ${new Date(proposal.endTime.toNumber() * 1000).toLocaleString()}`);
      }
    }

    // Instructions for manual testing
    console.log('\n🎯 Manual Testing Instructions:');
    console.log('='.repeat(50));
    console.log('1. Connect to Hardhat console:');
    console.log(`   npx hardhat console --network polygonAmoy`);
    console.log('');
    console.log('2. Get contract instances:');
    console.log(`   const nft = await ethers.getContractAt("WE3HomeNFT", "${nftAddress}");`);
    console.log(`   const voting = await ethers.getContractAt("SimpleNftVoting", "${votingAddress}");`);
    console.log('');
    console.log('3. Create a proposal:');
    console.log(`   await voting.createProposal("Your proposal description");`);
    console.log('');
    console.log('4. Vote (if you own NFT token 1):');
    console.log(`   await voting.vote(1, 1, 2); // proposalId, tokenId, voteOption (2=Yes)`);
    console.log('');
    console.log('5. Check results:');
    console.log(`   await voting.getProposal(1);`);
    console.log(`   await voting.hasProposalPassed(1);`);

    console.log('\n🎉 Live Testing Complete!');
    console.log('Your DAO voting system is fully functional on testnet!');

  } catch (error) {
    console.error('❌ Error during testing:', error);
  }
}

// Execute test
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

export default main;
