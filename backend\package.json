{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint .", "hardhat": "hardhat", "compile": "hardhat compile", "deploy": "hardhat run scripts/deploy.js", "deploy:testnet": "hardhat run scripts/deploy.js --network sepolia", "verify": "hardhat verify"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "ethers": "^6.14.3", "express": "^5.1.0", "mongoose": "^8.15.1"}, "devDependencies": {"hardhat": "^2.22.0", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@openzeppelin/contracts": "^5.0.0"}}