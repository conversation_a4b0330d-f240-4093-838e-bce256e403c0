{"name": "NFT Voting Plugin", "description": "A plugin that allows NFT holders to vote on DAO proposals using ERC-721 tokens. Each NFT represents 1 vote and can only be used once per proposal.", "version": "1.0.0", "author": "Aragon", "license": "AGPL-3.0-or-later", "homepage": "https://github.com/aragon/nft-voting-plugin", "repository": {"type": "git", "url": "https://github.com/aragon/nft-voting-plugin.git"}, "keywords": ["aragon", "dao", "plugin", "voting", "nft", "erc721", "governance"], "tags": ["voting", "nft", "erc721", "governance"], "category": "governance", "icon": "https://github.com/aragon/nft-voting-plugin/raw/main/assets/icon.svg", "images": ["https://github.com/aragon/nft-voting-plugin/raw/main/assets/screenshot1.png", "https://github.com/aragon/nft-voting-plugin/raw/main/assets/screenshot2.png"], "permissions": [{"id": "CREATE_PROPOSAL_PERMISSION", "name": "Create Proposal", "description": "Allows creating new proposals for the DAO to vote on", "type": "grant", "where": "plugin", "who": "dao"}, {"id": "EXECUTE_PERMISSION", "name": "Execute Actions", "description": "Allows the plugin to execute approved proposals on behalf of the DAO", "type": "grant", "where": "dao", "who": "plugin"}, {"id": "UPDATE_VOTING_SETTINGS_PERMISSION", "name": "Update Voting Settings", "description": "Allows updating the voting parameters and thresholds", "type": "grant", "where": "plugin", "who": "dao"}, {"id": "EXECUTE_PROPOSAL_PERMISSION", "name": "Execute Proposal", "description": "Allows executing proposals that have passed the voting requirements", "type": "grant", "where": "plugin", "who": "dao"}], "roles": [{"id": "MANAGER_ROLE", "name": "Manager", "description": "Manager role for DAO operations and oversight", "permissions": ["CREATE_PROPOSAL_PERMISSION", "UPDATE_VOTING_SETTINGS_PERMISSION"]}, {"id": "COUNCIL_ROLE", "name": "Council Member", "description": "Council member role for governance participation", "permissions": ["CREATE_PROPOSAL_PERMISSION"]}], "features": [{"name": "NFT-Based Voting", "description": "Each ERC-721 token represents one vote"}, {"name": "Double Voting Prevention", "description": "Each token can only vote once per proposal"}, {"name": "Configurable Thresholds", "description": "Customizable support and participation thresholds"}, {"name": "Role Management", "description": "Built-in support for Manager and Council role appointments"}, {"name": "Early Execution", "description": "Proposals can be executed early if they reach decisive thresholds"}, {"name": "Vote Replacement", "description": "Optional vote replacement mode for changing votes"}], "installation": {"parameters": [{"name": "nftContract", "type": "address", "description": "The address of the ERC-721 contract used for voting", "required": true}, {"name": "votingSettings", "type": "object", "description": "The voting configuration parameters", "required": true, "properties": {"votingMode": {"type": "enum", "values": ["Standard", "EarlyExecution", "VoteReplacement"], "default": "Standard", "description": "The voting mode to use"}, "supportThreshold": {"type": "uint32", "min": 0, "max": 1000000, "default": 500000, "description": "Support threshold in basis points (500000 = 50%)"}, "minParticipation": {"type": "uint32", "min": 0, "max": 1000000, "default": 250000, "description": "Minimum participation in basis points (250000 = 25%)"}, "minDuration": {"type": "uint64", "min": 3600, "max": 31536000, "default": 86400, "description": "Minimum voting duration in seconds (86400 = 1 day)"}, "minProposerVotingPower": {"type": "uint256", "min": 1, "default": 1, "description": "Minimum NFTs required to create a proposal"}}}]}, "usage": {"examples": [{"title": "Create Manager Appointment Proposal", "description": "Create a proposal to appoint a new Manager", "code": "createProposal(metadata, managerAppointmentActions, 0, startDate, endDate)"}, {"title": "Vote with NFT", "description": "Cast a vote using an owned NFT token", "code": "vote(proposalId, tokenId, VoteOption.Yes)"}, {"title": "Execute Passed Proposal", "description": "Execute a proposal that has met the voting requirements", "code": "execute(proposalId)"}]}, "compatibility": {"aragon": "^1.4.0", "solidity": "0.8.17", "evm": "london"}, "networks": {"mainnet": {"supported": true, "chainId": 1}, "polygon": {"supported": true, "chainId": 137}, "sepolia": {"supported": true, "chainId": 11155111}, "polygonMumbai": {"supported": true, "chainId": 80001}}, "documentation": {"userGuide": "https://docs.aragon.org/plugins/nft-voting", "developerGuide": "https://github.com/aragon/nft-voting-plugin/blob/main/docs/developer-guide.md", "api": "https://github.com/aragon/nft-voting-plugin/blob/main/docs/api.md"}}