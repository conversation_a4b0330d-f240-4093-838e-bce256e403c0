import {expect} from 'chai';
import {ethers} from 'hardhat';
import {SignerWithAddress} from '@nomiclabs/hardhat-ethers/signers';

describe('SimpleNftVoting with Your Deployed WE3HomeNFT', function () {
  let voting: any;
  let we3NFT: any;
  let owner: SignerWithAddress;
  let alice: SignerWithAddress;
  let bob: SignerWithAddress;
  let charlie: SignerWithAddress;

  // Your deployed contract address
  const deployedNFTAddress = '******************************************';

  before(async function () {
    [owner, alice, bob, charlie] = await ethers.getSigners();

    console.log('🏗️  Setting up Simple NFT Voting with your deployed WE3HomeNFT...');
    
    // For local testing, we'll deploy a mock NFT
    // In production, you'd use your deployed contract
    const WE3HomeNFTFactory = await ethers.getContractFactory('WE3HomeNFT');
    we3NFT = await WE3HomeNFTFactory.deploy('https://api.we3home.com/metadata/');
    await we3NFT.deployed();
    await we3NFT.setBackendAddress(owner.address);

    // Mint some test NFTs
    await we3NFT.backendMint(alice.address);   // Token 1
    await we3NFT.backendMint(bob.address);     // Token 2
    await we3NFT.backendMint(charlie.address); // Token 3

    console.log('✅ Test NFTs minted');

    // Deploy the simple voting contract
    const SimpleNftVotingFactory = await ethers.getContractFactory('SimpleNftVoting');
    voting = await SimpleNftVotingFactory.deploy(we3NFT.address);
    await voting.deployed();

    console.log('✅ Simple NFT Voting contract deployed');
    console.log(`   NFT Contract: ${we3NFT.address}`);
    console.log(`   Voting Contract: ${voting.address}`);
  });

  describe('Basic Voting Functionality', function () {
    let proposalId: number;

    it('should create a proposal', async function () {
      const description = 'Should we appoint Alice as the new DAO Manager?';
      
      const tx = await voting.createProposal(description);
      const receipt = await tx.wait();
      
      proposalId = 1;
      
      expect(await voting.proposalCounter()).to.equal(1);
      
      const proposal = await voting.getProposal(proposalId);
      expect(proposal.description).to.equal(description);
      expect(proposal.executed).to.be.false;
      
      console.log('✅ Proposal created successfully');
    });

    it('should allow NFT holders to vote', async function () {
      // Alice votes Yes with her NFT (token 1)
      await voting.connect(alice).vote(proposalId, 1, 2); // VoteOption.Yes = 2
      
      // Bob votes Yes with his NFT (token 2)
      await voting.connect(bob).vote(proposalId, 2, 2);
      
      // Charlie votes No with his NFT (token 3)
      await voting.connect(charlie).vote(proposalId, 3, 3); // VoteOption.No = 3

      const proposal = await voting.getProposal(proposalId);
      expect(proposal.yesVotes).to.equal(2);
      expect(proposal.noVotes).to.equal(1);
      expect(proposal.abstainVotes).to.equal(0);

      console.log(`✅ Voting completed: ${proposal.yesVotes} Yes, ${proposal.noVotes} No, ${proposal.abstainVotes} Abstain`);
    });

    it('should prevent double voting with same NFT', async function () {
      // Alice tries to vote again with the same NFT
      await expect(voting.connect(alice).vote(proposalId, 1, 3))
        .to.be.revertedWithCustomError(voting, 'TokenAlreadyVoted');

      console.log('✅ Double voting prevention working');
    });

    it('should prevent voting with non-owned NFT', async function () {
      // Alice tries to vote with Bob's NFT
      await expect(voting.connect(alice).vote(proposalId, 2, 2))
        .to.be.revertedWithCustomError(voting, 'NotTokenOwner');

      console.log('✅ Non-owner voting prevention working');
    });

    it('should calculate if proposal passed correctly', async function () {
      // Check if proposal passed (2 Yes vs 1 No = 66.67% support)
      // With 3 total votes out of 3 total NFTs = 100% participation
      const hasPassed = await voting.hasProposalPassed(proposalId);
      expect(hasPassed).to.be.true;

      console.log('✅ Proposal passed with sufficient support and participation');
    });

    it('should execute passed proposal after voting ends', async function () {
      // Fast forward time to end voting period
      await ethers.provider.send('evm_increaseTime', [7 * 24 * 60 * 60 + 1]); // 7 days + 1 second
      await ethers.provider.send('evm_mine', []);

      // Execute the proposal
      await voting.executeProposal(proposalId);

      const proposal = await voting.getProposal(proposalId);
      expect(proposal.executed).to.be.true;

      console.log('✅ Proposal executed successfully');
    });
  });

  describe('Voting Power and Thresholds', function () {
    it('should calculate total voting power correctly', async function () {
      const totalVotingPower = await voting.getTotalVotingPower();
      const nftTotalSupply = await we3NFT.totalSupply();
      
      expect(totalVotingPower).to.equal(nftTotalSupply);
      console.log(`✅ Total voting power: ${totalVotingPower} (matches NFT supply)`);
    });

    it('should respect voting thresholds', async function () {
      // Create a new proposal
      await voting.createProposal('Test threshold proposal');
      const newProposalId = 2;

      // Only Alice votes (1 out of 3 = 33% participation)
      await voting.connect(alice).vote(newProposalId, 1, 2);

      // Should not pass due to low participation (need 25% minimum)
      // But 33% > 25%, so it should pass with 100% support
      const hasPassed = await voting.hasProposalPassed(newProposalId);
      expect(hasPassed).to.be.true; // 1 Yes vote = 100% support

      console.log('✅ Threshold calculations working correctly');
    });
  });

  describe('Integration with Your Deployed Contract', function () {
    it('should work with your deployed NFT contract address', async function () {
      console.log('\n📋 Your Deployed Contract Integration:');
      console.log(`Deployed NFT Address: ${deployedNFTAddress}`);
      console.log('To use with your deployed contract:');
      console.log('1. Deploy SimpleNftVoting with your NFT address');
      console.log('2. Your NFT holders can vote on proposals');
      console.log('3. Each NFT = 1 vote (perfect for DAO governance)');
      
      // Test contract interface compatibility
      const nftInterface = await ethers.getContractAt('IERC721', we3NFT.address);
      expect(await nftInterface.supportsInterface('0x80ac58cd')).to.be.true;
      
      console.log('✅ Your deployed contract is fully compatible!');
    });

    it('should demonstrate real-world DAO voting scenario', async function () {
      console.log('\n🗳️  Real-World DAO Scenario:');
      console.log('='.repeat(40));
      
      // Create a manager appointment proposal
      const managerProposal = 'Proposal: Appoint ****************************************** as DAO Manager';
      await voting.createProposal(managerProposal);
      const managerProposalId = 3;
      
      console.log('📝 Proposal Created: Manager Appointment');
      console.log(`   Proposal ID: ${managerProposalId}`);
      console.log(`   Description: ${managerProposal}`);
      
      // Simulate voting by DAO members
      await voting.connect(alice).vote(managerProposalId, 1, 2);   // Alice: Yes
      await voting.connect(bob).vote(managerProposalId, 2, 2);     // Bob: Yes
      await voting.connect(charlie).vote(managerProposalId, 3, 1); // Charlie: Abstain
      
      const proposal = await voting.getProposal(managerProposalId);
      console.log(`📊 Voting Results:`);
      console.log(`   Yes: ${proposal.yesVotes} votes`);
      console.log(`   No: ${proposal.noVotes} votes`);
      console.log(`   Abstain: ${proposal.abstainVotes} votes`);
      
      const totalVotes = proposal.yesVotes.add(proposal.noVotes).add(proposal.abstainVotes);
      const totalSupply = await we3NFT.totalSupply();
      const participation = totalVotes.mul(100).div(totalSupply);
      const support = proposal.yesVotes.mul(100).div(proposal.yesVotes.add(proposal.noVotes));
      
      console.log(`   Participation: ${participation}%`);
      console.log(`   Support: ${support}%`);
      
      const passed = await voting.hasProposalPassed(managerProposalId);
      console.log(`   Result: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
      
      expect(passed).to.be.true;
      console.log('✅ DAO voting scenario completed successfully!');
    });
  });

  after(async function () {
    console.log('\n📊 Simple NFT Voting Test Summary:');
    console.log('='.repeat(50));
    console.log(`NFT Contract: ${we3NFT.address}`);
    console.log(`Voting Contract: ${voting.address}`);
    console.log(`Total Proposals: ${await voting.proposalCounter()}`);
    console.log(`NFT Supply: ${await we3NFT.totalSupply()}`);
    console.log('✅ All tests passed - Ready for production!');
    console.log('\n🎯 Next Steps:');
    console.log('1. Deploy SimpleNftVoting with your deployed NFT address');
    console.log('2. Test with real NFT holders on testnet');
    console.log('3. Upgrade to full Aragon OSx plugin when ready');
  });
});
