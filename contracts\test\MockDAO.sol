// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

/// @title MockDAO
/// @notice Simple mock DAO contract for testing role management
contract MockDAO {
    /// @notice Mapping to store role assignments
    mapping(bytes32 => mapping(address => bool)) public roles;

    /// @notice Event emitted when a role is granted
    event RoleGranted(bytes32 indexed role, address indexed account);

    /// @notice Event emitted when a role is revoked
    event RoleRevoked(bytes32 indexed role, address indexed account);

    /// @notice Grants a role to an account
    /// @param role The role identifier
    /// @param account The account to grant the role to
    function grantRole(bytes32 role, address account) external {
        roles[role][account] = true;
        emit RoleGranted(role, account);
    }

    /// @notice Revokes a role from an account
    /// @param role The role identifier
    /// @param account The account to revoke the role from
    function revokeRole(bytes32 role, address account) external {
        roles[role][account] = false;
        emit RoleRevoked(role, account);
    }

    /// @notice Checks if an account has a role
    /// @param role The role identifier
    /// @param account The account to check
    /// @return True if the account has the role
    function hasRole(bytes32 role, address account) external view returns (bool) {
        return roles[role][account];
    }
}
