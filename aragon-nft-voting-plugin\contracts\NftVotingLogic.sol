// SPDX-License-Identifier: AGPL-3.0-or-later
pragma solidity 0.8.17;

import {IERC721} from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import {IERC721Enumerable} from "@openzeppelin/contracts/token/ERC721/extensions/IERC721Enumerable.sol";
import {IERC165} from "@openzeppelin/contracts/utils/introspection/IERC165.sol";
import {SafeCast} from "@openzeppelin/contracts/utils/math/SafeCast.sol";

/// @title NftVotingLogic
/// <AUTHOR>
/// @notice Library for NFT-based voting logic with 1 NFT = 1 vote principle
/// @dev Handles vote counting, double voting prevention, and threshold calculations
library NftVotingLogic {
    using SafeCast for uint256;

    /// @notice The different vote options available
    enum VoteOption {
        None,
        Abstain,
        Yes,
        No
    }

    /// @notice A container for the proposal vote tally
    /// @param abstain The number of abstain votes
    /// @param yes The number of yes votes
    /// @param no The number of no votes
    struct Tally {
        uint256 abstain;
        uint256 yes;
        uint256 no;
    }

    /// @notice A container for voting thresholds
    /// @param supportThreshold The support threshold (in basis points, 1000000 = 100%)
    /// @param minParticipation The minimum participation (in basis points, 1000000 = 100%)
    struct VotingThresholds {
        uint32 supportThreshold;
        uint32 minParticipation;
    }

    /// @notice Thrown when a token has already been used to vote
    /// @param proposalId The proposal identifier
    /// @param tokenId The token ID
    error TokenAlreadyVoted(uint256 proposalId, uint256 tokenId);

    /// @notice Thrown when the voter doesn't own the NFT
    /// @param tokenId The token ID
    /// @param voter The voter address
    error VoteCastForbidden(uint256 tokenId, address voter);

    /// @notice Thrown when an invalid vote option is provided
    /// @param voteOption The invalid vote option
    error InvalidVoteOption(VoteOption voteOption);

    /// @notice Thrown when threshold values are invalid
    /// @param threshold The invalid threshold value
    error InvalidThreshold(uint32 threshold);

    /// @notice Validates that a voter owns the NFT token
    /// @param _nftToken The NFT contract
    /// @param _tokenId The token ID
    /// @param _voter The voter address
    /// @return isValid True if the voter owns the token
    function validateTokenOwnership(
        IERC721 _nftToken,
        uint256 _tokenId,
        address _voter
    ) internal view returns (bool isValid) {
        try _nftToken.ownerOf(_tokenId) returns (address owner) {
            return owner == _voter;
        } catch {
            return false;
        }
    }

    /// @notice Validates that a token hasn't been used to vote
    /// @param _voters Mapping of tokenId to hasVoted
    /// @param _tokenId The token ID
    /// @return isValid True if the token hasn't voted
    function validateTokenNotVoted(
        mapping(uint256 => bool) storage _voters,
        uint256 _tokenId
    ) internal view returns (bool isValid) {
        return !_voters[_tokenId];
    }

    /// @notice Validates the vote option
    /// @param _voteOption The vote option
    /// @return isValid True if the vote option is valid
    function validateVoteOption(VoteOption _voteOption) internal pure returns (bool isValid) {
        return _voteOption == VoteOption.Yes || 
               _voteOption == VoteOption.No || 
               _voteOption == VoteOption.Abstain;
    }

    /// @notice Casts a vote and updates the tally
    /// @param _tally The current vote tally
    /// @param _voters Mapping of tokenId to hasVoted
    /// @param _tokenId The token ID
    /// @param _voteOption The vote option
    function castVote(
        Tally storage _tally,
        mapping(uint256 => bool) storage _voters,
        uint256 _tokenId,
        VoteOption _voteOption
    ) internal {
        // Validate vote option
        if (!validateVoteOption(_voteOption)) {
            revert InvalidVoteOption(_voteOption);
        }

        // Mark token as having voted
        _voters[_tokenId] = true;

        // Update tally based on vote option
        if (_voteOption == VoteOption.Yes) {
            _tally.yes += 1;
        } else if (_voteOption == VoteOption.No) {
            _tally.no += 1;
        } else if (_voteOption == VoteOption.Abstain) {
            _tally.abstain += 1;
        }
    }

    /// @notice Calculates if a proposal has reached the required thresholds
    /// @param _tally The vote tally
    /// @param _thresholds The voting thresholds
    /// @param _totalVotingPower The total voting power at snapshot
    /// @return hasReachedThreshold True if thresholds are met
    function hasReachedThreshold(
        Tally memory _tally,
        VotingThresholds memory _thresholds,
        uint256 _totalVotingPower
    ) internal pure returns (bool hasReachedThreshold) {
        // Calculate total votes cast
        uint256 totalVotes = _tally.yes + _tally.no + _tally.abstain;

        // Check minimum participation threshold
        // totalVotes / totalVotingPower >= minParticipation / 1000000
        if (totalVotes * 1000000 < _totalVotingPower * _thresholds.minParticipation) {
            return false;
        }

        // Calculate support votes (yes + no, excluding abstain)
        uint256 supportVotes = _tally.yes + _tally.no;
        
        // If no support votes, proposal fails
        if (supportVotes == 0) {
            return false;
        }

        // Check support threshold
        // yes / (yes + no) >= supportThreshold / 1000000
        return _tally.yes * 1000000 >= supportVotes * _thresholds.supportThreshold;
    }

    /// @notice Gets the total voting power from an NFT contract
    /// @param _nftToken The NFT contract
    /// @param _blockNumber The block number (for historical queries)
    /// @return totalPower The total voting power
    function getTotalVotingPower(
        IERC721 _nftToken,
        uint256 _blockNumber
    ) internal view returns (uint256 totalPower) {
        // Try to get total supply if the contract supports ERC721Enumerable
        try IERC165(address(_nftToken)).supportsInterface(0x780e9d63) returns (bool supportsEnumerable) {
            if (supportsEnumerable) {
                try IERC721Enumerable(address(_nftToken)).totalSupply() returns (uint256 supply) {
                    return supply;
                } catch {
                    // Fall through to alternative method
                }
            }
        } catch {
            // Fall through to alternative method
        }

        // Alternative: Try to call totalSupply() directly (many NFT contracts have this)
        try _callTotalSupply(_nftToken) returns (uint256 supply) {
            return supply;
        } catch {
            // Fallback: Return a reasonable default or revert
            // In production, this should be handled more gracefully
            return 10000; // This should be configurable or tracked separately
        }

        // Silence unused parameter warning
        _blockNumber;
    }

    /// @notice Gets the voting power of an account
    /// @param _nftToken The NFT contract
    /// @param _account The account address
    /// @param _blockNumber The block number (for historical queries)
    /// @return votingPower The voting power (number of NFTs owned)
    function getVotingPower(
        IERC721 _nftToken,
        address _account,
        uint256 _blockNumber
    ) internal view returns (uint256 votingPower) {
        // For current implementation, just return current balance
        // In a full implementation, you'd need to track historical balances
        _blockNumber; // Silence unused parameter warning
        return _nftToken.balanceOf(_account);
    }

    /// @notice Validates voting thresholds
    /// @param _thresholds The voting thresholds to validate
    /// @return isValid True if thresholds are valid
    function validateThresholds(VotingThresholds memory _thresholds) internal pure returns (bool isValid) {
        // Support threshold must be <= 100%
        if (_thresholds.supportThreshold > 1000000) {
            return false;
        }

        // Min participation must be <= 100%
        if (_thresholds.minParticipation > 1000000) {
            return false;
        }

        return true;
    }

    /// @notice Calculates the current vote percentages
    /// @param _tally The vote tally
    /// @return yesPercentage Percentage of yes votes (in basis points)
    /// @return noPercentage Percentage of no votes (in basis points)
    /// @return abstainPercentage Percentage of abstain votes (in basis points)
    function calculateVotePercentages(Tally memory _tally)
        internal
        pure
        returns (
            uint256 yesPercentage,
            uint256 noPercentage,
            uint256 abstainPercentage
        )
    {
        uint256 totalVotes = _tally.yes + _tally.no + _tally.abstain;
        
        if (totalVotes == 0) {
            return (0, 0, 0);
        }

        yesPercentage = (_tally.yes * 1000000) / totalVotes;
        noPercentage = (_tally.no * 1000000) / totalVotes;
        abstainPercentage = (_tally.abstain * 1000000) / totalVotes;
    }

    /// @notice Internal function to call totalSupply() on NFT contract
    /// @param _nftToken The NFT contract
    /// @return supply The total supply
    function _callTotalSupply(IERC721 _nftToken) private view returns (uint256 supply) {
        (bool success, bytes memory data) = address(_nftToken).staticcall(
            abi.encodeWithSignature("totalSupply()")
        );
        
        if (success && data.length == 32) {
            return abi.decode(data, (uint256));
        } else {
            revert("TotalSupply call failed");
        }
    }

    /// @notice Checks if early execution is possible
    /// @param _tally The current vote tally
    /// @param _thresholds The voting thresholds
    /// @param _totalVotingPower The total voting power
    /// @return canExecuteEarly True if early execution is possible
    function canExecuteEarly(
        Tally memory _tally,
        VotingThresholds memory _thresholds,
        uint256 _totalVotingPower
    ) internal pure returns (bool canExecuteEarly) {
        // For early execution, we need to check if the proposal can still fail
        // even if all remaining tokens vote against it
        
        uint256 totalVotesCast = _tally.yes + _tally.no + _tally.abstain;
        uint256 remainingVotes = _totalVotingPower > totalVotesCast ? 
            _totalVotingPower - totalVotesCast : 0;

        // Simulate worst case: all remaining votes go to "no"
        Tally memory worstCaseTally = Tally({
            yes: _tally.yes,
            no: _tally.no + remainingVotes,
            abstain: _tally.abstain
        });

        // If proposal would still pass in worst case, early execution is possible
        return hasReachedThreshold(worstCaseTally, _thresholds, _totalVotingPower);
    }
}
