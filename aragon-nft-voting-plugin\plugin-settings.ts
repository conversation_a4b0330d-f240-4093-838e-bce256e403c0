export const PLUGIN_REPO_ENS_NAME = 'nft-voting-plugin';
export const VERSION = {
  release: 1, // Increment this number ONLY if breaking/incompatible changes were made. Updates between releases are NOT possible.
  build: 1, // Increment this number if non-breaking/compatible changes were made. Updates to newer builds are possible.
};

export const METADATA = {
  name: 'NFT Voting Plugin',
  description: 'A plugin that allows NFT holders to vote on DAO proposals using ERC-721 tokens',
  image: 'https://github.com/aragon/nft-voting-plugin/raw/main/assets/logo.png',
  tags: ['voting', 'nft', 'erc721', 'governance'],
};

// Plugin contract constructor parameters
export const PLUGIN_CONTRACT_NAME = 'NftVotingPlugin';
export const PLUGIN_SETUP_CONTRACT_NAME = 'NftVotingPluginSetup';

// Default voting settings
export const DEFAULT_VOTING_SETTINGS = {
  votingMode: 0, // Standard voting mode
  supportThreshold: 500000, // 50% (in basis points, 1000000 = 100%)
  minParticipation: 250000, // 25% (in basis points)
  minDuration: 86400, // 1 day in seconds
  minProposerVotingPower: 1, // Minimum 1 NFT to create proposal
};

// Role identifiers (keccak256 hashes)
export const ROLES = {
  MANAGER_ROLE: '0x241ecf16d79d0f8dbfb92cbc07fe17840425976cf0667f022fe9877caa831b08',
  COUNCIL_ROLE: '0x71840dc4906352362b0cdaf79870196c8e42acafade72d5d5a6d59291253ceb1',
  EXECUTE_PERMISSION: '0x2dd8cc6b5a4e5c7b1f1b6c8e8b4c8e8b4c8e8b4c8e8b4c8e8b4c8e8b4c8e8b4c',
};
