{"extends": "solhint:recommended", "plugins": ["prettier"], "rules": {"code-complexity": ["error", 7], "compiler-version": ["error", "^0.8.0"], "const-name-snakecase": "off", "constructor-syntax": "error", "func-visibility": ["error", {"ignoreConstructors": true}], "max-line-length": ["error", 120], "not-rely-on-time": "off", "prettier/prettier": "error", "reason-string": ["warn", {"maxLength": 64}]}}