{"name": "nft-minting-platform", "version": "1.0.0", "description": "A complete full-stack NFT minting platform with email registration and blockchain integration", "private": true, "scripts": {"dev": "concurrently \"npm run dev --prefix frontend\" \"npm run dev --prefix backend\"", "start": "concurrently \"npm run start --prefix frontend\" \"npm run start --prefix backend\"", "build": "npm run build --prefix frontend", "build:contracts": "hardhat compile", "install-all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../aragon-nft-voting-plugin && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules && hardhat clean", "test": "npm run test --prefix backend", "test:contracts": "hardhat test", "test:nft": "hardhat test test/WE3HomeNFT.test.ts", "test:integration": "hardhat test test/WE3HomeNFT-VotingPlugin-Integration.test.ts", "test:plugin": "cd aragon-nft-voting-plugin && npm test", "lint": "npm run lint --prefix frontend", "deploy": "npm run build && npm run start", "deploy:nft": "hardhat run scripts/deploy-nft.ts", "deploy:testnet": "hardhat run scripts/deploy-testnet.ts --network sepolia", "deploy:plugin": "cd aragon-nft-voting-plugin && npm run deploy", "verify:nft": "hardhat verify --network sepolia", "typechain": "hardhat typechain", "coverage": "hardhat coverage"}, "keywords": ["nft", "blockchain", "ethereum", "minting", "web3", "nextjs", "nodejs", "mongodb", "aragon", "dao", "voting", "governance", "erc721"], "author": "Your Name", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^1.0.5", "@nomicfoundation/hardhat-network-helpers": "^1.0.8", "@nomicfoundation/hardhat-verify": "^1.0.4", "@nomiclabs/hardhat-ethers": "^2.2.1", "@nomiclabs/hardhat-etherscan": "^3.1.8", "@typechain/ethers-v5": "^10.1.1", "@typechain/hardhat": "^6.1.4", "@types/chai": "^4.3.4", "@types/mocha": "^10.0.0", "@types/node": "^18.11.9", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "chai": "^4.3.7", "concurrently": "^8.2.2", "dotenv": "^16.3.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "ethers": "^5.7.2", "hardhat": "^2.22.15", "hardhat-gas-reporter": "^1.0.9", "nodemon": "^3.0.0", "prettier": "^2.8.8", "prettier-plugin-solidity": "^1.1.3", "solidity-coverage": "^0.8.2", "ts-node": "^10.9.1", "typechain": "^8.3.2", "typescript": "^5.2.2"}, "dependencies": {"@openzeppelin/contracts": "^4.9.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}