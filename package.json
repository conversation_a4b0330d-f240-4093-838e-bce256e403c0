{"name": "nft-minting-platform", "version": "1.0.0", "description": "A complete full-stack NFT minting platform with email registration and blockchain integration", "private": true, "scripts": {"dev": "concurrently \"npm run dev --prefix frontend\" \"npm run dev --prefix backend\"", "start": "concurrently \"npm run start --prefix frontend\" \"npm run start --prefix backend\"", "build": "npm run build --prefix frontend", "install-all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules", "test": "npm run test --prefix backend", "lint": "npm run lint --prefix frontend", "deploy": "npm run build && npm run start"}, "keywords": ["nft", "blockchain", "ethereum", "minting", "web3", "nextjs", "nodejs", "mongodb", "dao", "voting"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}