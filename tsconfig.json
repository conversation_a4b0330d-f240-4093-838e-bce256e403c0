{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "typeRoots": ["./node_modules/@types", "./typechain"]}, "include": ["./contracts/**/*", "./scripts/**/*", "./test/**/*", "./hardhat.config.ts"], "exclude": ["node_modules", "artifacts", "cache", "coverage", "dist", "frontend", "backend", "aragon-nft-voting-plugin"]}