const mongoose = require('mongoose');

const nftUserSchema = new mongoose.Schema({
  email: { 
    type: String, 
    required: true, 
    unique: true, 
    lowercase: true, 
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  wallet: { 
    type: String, 
    required: true, 
    unique: true, 
    lowercase: true, 
    trim: true,
    match: [/^0x[a-fA-F0-9]{40}$/, 'Please enter a valid Ethereum wallet address']
  },
  tokenId: { 
    type: Number, 
    required: true, 
    unique: true 
  },
  transactionHash: {
    type: String,
    required: true,
    unique: true
  },
  mintedAt: { 
    type: Date, 
    default: Date.now 
  },
  status: {
    type: String,
    enum: ['pending', 'minted', 'failed'],
    default: 'pending'
  },
  metadata: {
    name: String,
    description: String,
    image: String,
    attributes: [{
      trait_type: String,
      value: String
    }]
  }
});

// Index for faster queries
nftUserSchema.index({ email: 1 });
nftUserSchema.index({ wallet: 1 });
nftUserSchema.index({ tokenId: 1 });
nftUserSchema.index({ status: 1 });

module.exports = mongoose.model('NftUser', nftUserSchema);
