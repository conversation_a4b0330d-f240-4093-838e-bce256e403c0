// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721.sol";

/// @title SimpleNftVoting
/// @notice A simple NFT-based voting contract that works with your deployed WE3HomeNFT
/// @dev This demonstrates the voting mechanics before full Aragon integration
contract SimpleNftVoting is Ownable {
    /// @notice The NFT contract used for voting
    IERC721 public immutable nftContract;

    /// @notice Vote options
    enum VoteOption { None, Abstain, Yes, No }

    /// @notice Proposal struct
    struct Proposal {
        string description;
        uint256 startTime;
        uint256 endTime;
        uint256 yesVotes;
        uint256 noVotes;
        uint256 abstainVotes;
        bool executed;
        mapping(uint256 => bool) hasVoted; // tokenId => hasVoted
    }

    /// @notice Mapping from proposal ID to proposal
    mapping(uint256 => Proposal) public proposals;

    /// @notice Current proposal counter
    uint256 public proposalCounter;

    /// @notice Voting duration (default 7 days)
    uint256 public votingDuration = 7 days;

    /// @notice Support threshold (50% = 500000)
    uint256 public supportThreshold = 500000; // 50%

    /// @notice Minimum participation (25% = 250000)
    uint256 public minParticipation = 250000; // 25%

    /// @notice Events
    event ProposalCreated(uint256 indexed proposalId, string description, uint256 startTime, uint256 endTime);
    event VoteCast(uint256 indexed proposalId, address indexed voter, uint256 indexed tokenId, VoteOption vote);
    event ProposalExecuted(uint256 indexed proposalId);

    /// @notice Errors
    error NotTokenOwner();
    error TokenAlreadyVoted();
    error VotingNotActive();
    error ProposalNotPassed();
    error ProposalAlreadyExecuted();

    /// @notice Constructor
    /// @param _nftContract Address of your deployed WE3HomeNFT contract
    constructor(address _nftContract) {
        nftContract = IERC721(_nftContract);
    }

    /// @notice Create a new proposal
    /// @param _description Description of the proposal
    /// @return proposalId The ID of the created proposal
    function createProposal(string calldata _description) external onlyOwner returns (uint256 proposalId) {
        proposalId = ++proposalCounter;
        
        Proposal storage proposal = proposals[proposalId];
        proposal.description = _description;
        proposal.startTime = block.timestamp;
        proposal.endTime = block.timestamp + votingDuration;

        emit ProposalCreated(proposalId, _description, proposal.startTime, proposal.endTime);
    }

    /// @notice Vote on a proposal with an NFT
    /// @param _proposalId The proposal to vote on
    /// @param _tokenId The NFT token ID to use for voting
    /// @param _vote The vote option
    function vote(uint256 _proposalId, uint256 _tokenId, VoteOption _vote) external {
        // Check if caller owns the NFT
        if (nftContract.ownerOf(_tokenId) != msg.sender) {
            revert NotTokenOwner();
        }

        Proposal storage proposal = proposals[_proposalId];

        // Check if voting is active
        if (block.timestamp < proposal.startTime || block.timestamp > proposal.endTime) {
            revert VotingNotActive();
        }

        // Check if token has already voted
        if (proposal.hasVoted[_tokenId]) {
            revert TokenAlreadyVoted();
        }

        // Mark token as voted
        proposal.hasVoted[_tokenId] = true;

        // Count the vote
        if (_vote == VoteOption.Yes) {
            proposal.yesVotes++;
        } else if (_vote == VoteOption.No) {
            proposal.noVotes++;
        } else if (_vote == VoteOption.Abstain) {
            proposal.abstainVotes++;
        }

        emit VoteCast(_proposalId, msg.sender, _tokenId, _vote);
    }

    /// @notice Execute a proposal if it has passed
    /// @param _proposalId The proposal to execute
    function executeProposal(uint256 _proposalId) external {
        Proposal storage proposal = proposals[_proposalId];

        // Check if already executed
        if (proposal.executed) {
            revert ProposalAlreadyExecuted();
        }

        // Check if voting has ended
        if (block.timestamp <= proposal.endTime) {
            revert VotingNotActive();
        }

        // Check if proposal passed
        if (!hasProposalPassed(_proposalId)) {
            revert ProposalNotPassed();
        }

        proposal.executed = true;

        emit ProposalExecuted(_proposalId);

        // Here you would implement the actual execution logic
        // For now, we just mark it as executed
    }

    /// @notice Check if a proposal has passed
    /// @param _proposalId The proposal to check
    /// @return True if the proposal has passed
    function hasProposalPassed(uint256 _proposalId) public view returns (bool) {
        Proposal storage proposal = proposals[_proposalId];

        uint256 totalVotes = proposal.yesVotes + proposal.noVotes + proposal.abstainVotes;
        uint256 totalSupply = getTotalVotingPower();

        // Check minimum participation
        if (totalVotes * 1000000 < totalSupply * minParticipation) {
            return false;
        }

        // Check support threshold (yes / (yes + no))
        uint256 supportVotes = proposal.yesVotes + proposal.noVotes;
        if (supportVotes == 0) {
            return false;
        }

        return proposal.yesVotes * 1000000 >= supportVotes * supportThreshold;
    }

    /// @notice Get total voting power (total NFT supply)
    /// @return The total voting power
    function getTotalVotingPower() public view returns (uint256) {
        // Try to get total supply from your NFT contract
        try this.callTotalSupply() returns (uint256 supply) {
            return supply;
        } catch {
            // Fallback: assume some reasonable number
            return 100; // You can adjust this
        }
    }

    /// @notice Helper function to call totalSupply on NFT contract
    /// @return The total supply
    function callTotalSupply() external view returns (uint256) {
        (bool success, bytes memory data) = address(nftContract).staticcall(
            abi.encodeWithSignature("totalSupply()")
        );
        require(success, "TotalSupply call failed");
        return abi.decode(data, (uint256));
    }

    /// @notice Get proposal details
    /// @param _proposalId The proposal ID
    /// @return description The proposal description
    /// @return startTime When voting started
    /// @return endTime When voting ends
    /// @return yesVotes Number of yes votes
    /// @return noVotes Number of no votes
    /// @return abstainVotes Number of abstain votes
    /// @return executed Whether the proposal is executed
    function getProposal(uint256 _proposalId) external view returns (
        string memory description,
        uint256 startTime,
        uint256 endTime,
        uint256 yesVotes,
        uint256 noVotes,
        uint256 abstainVotes,
        bool executed
    ) {
        Proposal storage proposal = proposals[_proposalId];
        return (
            proposal.description,
            proposal.startTime,
            proposal.endTime,
            proposal.yesVotes,
            proposal.noVotes,
            proposal.abstainVotes,
            proposal.executed
        );
    }

    /// @notice Check if a token has voted on a proposal
    /// @param _proposalId The proposal ID
    /// @param _tokenId The token ID
    /// @return True if the token has voted
    function hasTokenVoted(uint256 _proposalId, uint256 _tokenId) external view returns (bool) {
        return proposals[_proposalId].hasVoted[_tokenId];
    }

    /// @notice Update voting parameters (only owner)
    /// @param _votingDuration New voting duration
    /// @param _supportThreshold New support threshold (in basis points)
    /// @param _minParticipation New minimum participation (in basis points)
    function updateVotingParameters(
        uint256 _votingDuration,
        uint256 _supportThreshold,
        uint256 _minParticipation
    ) external onlyOwner {
        require(_supportThreshold <= 1000000, "Support threshold too high");
        require(_minParticipation <= 1000000, "Min participation too high");
        require(_votingDuration >= 1 hours, "Voting duration too short");

        votingDuration = _votingDuration;
        supportThreshold = _supportThreshold;
        minParticipation = _minParticipation;
    }
}
